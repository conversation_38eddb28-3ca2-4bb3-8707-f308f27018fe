"""
Database models for the Weight Loss Tracker application
"""
from sqlalchemy import Column, Integer, Float, String, DateTime, Boolean, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

# Association table for meal ingredients
meal_ingredients = Table(
    'meal_ingredients',
    Base.metadata,
    Column('meal_id', Integer, ForeignKey('meals.id')),
    Column('food_item_id', Integer, ForeignKey('food_items.id')),
    Column('quantity', Float, default=1.0),
    Column('unit', String(50), default='serving')
)

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    first_name = Column(String(100))
    last_name = Column(String(100))
    date_of_birth = Column(DateTime)
    gender = Column(String(10))
    height_inches = Column(Float)
    activity_level = Column(String(50))  # sedentary, lightly_active, moderately_active, very_active, extra_active
    goal_weight_lbs = Column(Float)
    goal_type = Column(String(50))  # lose_weight, maintain_weight, gain_weight
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    
    # Relationships
    biometric_entries = relationship("BiometricEntry", back_populates="user")
    nutrition_entries = relationship("NutritionEntry", back_populates="user")
    meals = relationship("Meal", back_populates="user")
    exercise_entries = relationship("ExerciseEntry", back_populates="user")

class BiometricEntry(Base):
    __tablename__ = "biometric_entries"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    recorded_at = Column(DateTime, default=func.now())
    
    # Core biometrics
    weight_lbs = Column(Float)
    body_fat_percentage = Column(Float)
    water_percentage = Column(Float)
    bone_mass_lbs = Column(Float)
    muscle_mass_lbs = Column(Float)
    visceral_fat_rating = Column(Float)
    bmi = Column(Float)
    
    # Calculated/derived metrics
    bmr_calculated = Column(Float)  # Calculated BMR
    bmr_predicted = Column(Float)   # AI-predicted BMR
    
    notes = Column(Text)
    
    # Relationships
    user = relationship("User", back_populates="biometric_entries")

class FoodItem(Base):
    __tablename__ = "food_items"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    brand = Column(String(255))
    barcode = Column(String(50))
    
    # Nutritional information per 100g (keeping metric for food database consistency)
    calories_per_100g = Column(Float, nullable=False)
    protein_per_100g = Column(Float, default=0)
    carbs_per_100g = Column(Float, default=0)
    fat_per_100g = Column(Float, default=0)
    fiber_per_100g = Column(Float, default=0)
    sugar_per_100g = Column(Float, default=0)
    sodium_per_100g = Column(Float, default=0)  # in mg

    # Serving information
    serving_size_g = Column(Float, default=100)
    serving_description = Column(String(100))
    
    created_at = Column(DateTime, server_default=func.now())
    is_verified = Column(Boolean, default=False)
    
    # Relationships
    nutrition_entries = relationship("NutritionEntry", back_populates="food_item")

class Meal(Base):
    __tablename__ = "meals"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    meal_type = Column(String(50))  # breakfast, lunch, dinner, snack
    is_template = Column(Boolean, default=False)  # True for saved meal templates
    
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="meals")
    food_items = relationship("FoodItem", secondary=meal_ingredients, backref="meals")
    nutrition_entries = relationship("NutritionEntry", back_populates="meal")

class NutritionEntry(Base):
    __tablename__ = "nutrition_entries"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    food_item_id = Column(Integer, ForeignKey("food_items.id"))
    meal_id = Column(Integer, ForeignKey("meals.id"))
    
    consumed_at = Column(DateTime, server_default=func.now())
    quantity = Column(Float, nullable=False)  # Amount consumed
    unit = Column(String(50), default='g')  # g, ml, serving, etc.
    
    # Calculated nutritional values for this entry
    calories = Column(Float)
    protein = Column(Float)
    carbs = Column(Float)
    fat = Column(Float)
    fiber = Column(Float)
    sugar = Column(Float)
    sodium = Column(Float)
    
    # Beverages
    water_ml = Column(Float, default=0)
    caffeine_mg = Column(Float, default=0)
    
    notes = Column(Text)
    
    # Relationships
    user = relationship("User", back_populates="nutrition_entries")
    food_item = relationship("FoodItem", back_populates="nutrition_entries")
    meal = relationship("Meal", back_populates="nutrition_entries")

class ExerciseType(Base):
    __tablename__ = "exercise_types"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, unique=True)
    category = Column(String(100))  # cardio, strength, flexibility, sports
    met_value = Column(Float)  # Metabolic equivalent for calorie calculation
    description = Column(Text)
    
    # Relationships
    exercise_entries = relationship("ExerciseEntry", back_populates="exercise_type")

class ExerciseEntry(Base):
    __tablename__ = "exercise_entries"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    exercise_type_id = Column(Integer, ForeignKey("exercise_types.id"))
    
    performed_at = Column(DateTime, server_default=func.now())
    duration_minutes = Column(Float)
    intensity = Column(String(50))  # low, moderate, high, very_high
    
    # Fitness tracker data
    calories_burned = Column(Float)
    steps = Column(Integer)
    distance_miles = Column(Float)
    heart_rate_avg = Column(Integer)
    heart_rate_max = Column(Integer)

    # Manual entry data
    sets = Column(Integer)
    reps = Column(Integer)
    weight_lbs = Column(Float)
    
    notes = Column(Text)
    
    # Relationships
    user = relationship("User", back_populates="exercise_entries")
    exercise_type = relationship("ExerciseType", back_populates="exercise_entries")
