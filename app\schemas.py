"""
Pydantic schemas for API request/response models
"""
from pydantic import BaseModel, EmailStr
from typing import Optional, List, Union
from datetime import datetime, date

# User schemas
class UserBase(BaseModel):
    email: EmailStr
    username: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    date_of_birth: Optional[Union[datetime, date, str]] = None
    gender: Optional[str] = None
    height_inches: Optional[float] = None
    activity_level: Optional[str] = None
    goal_weight_lbs: Optional[float] = None
    goal_type: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    height_inches: Optional[float] = None
    activity_level: Optional[str] = None
    goal_weight_lbs: Optional[float] = None
    goal_type: Optional[str] = None

class User(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True

# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# Biometric schemas
class BiometricEntryBase(BaseModel):
    weight_lbs: Optional[float] = None
    body_fat_percentage: Optional[float] = None
    water_percentage: Optional[float] = None
    bone_mass_lbs: Optional[float] = None
    muscle_mass_lbs: Optional[float] = None
    visceral_fat_rating: Optional[float] = None
    bmi: Optional[float] = None
    notes: Optional[str] = None

class BiometricEntryCreate(BiometricEntryBase):
    recorded_at: Optional[datetime] = None

class BiometricEntry(BiometricEntryBase):
    id: int
    user_id: int
    recorded_at: datetime
    bmr_calculated: Optional[float] = None
    bmr_predicted: Optional[float] = None
    
    class Config:
        from_attributes = True

# Food Item schemas
class FoodItemBase(BaseModel):
    name: str
    brand: Optional[str] = None
    barcode: Optional[str] = None
    calories_per_100g: float
    protein_per_100g: float = 0
    carbs_per_100g: float = 0
    fat_per_100g: float = 0
    fiber_per_100g: float = 0
    sugar_per_100g: float = 0
    sodium_per_100g: float = 0
    serving_size_g: float = 100
    serving_description: Optional[str] = None

class FoodItemCreate(FoodItemBase):
    pass

class FoodItem(FoodItemBase):
    id: int
    created_at: datetime
    is_verified: bool
    
    class Config:
        from_attributes = True

# Nutrition Entry schemas
class NutritionEntryBase(BaseModel):
    food_item_id: Optional[int] = None
    meal_id: Optional[int] = None
    quantity: float
    unit: str = 'g'
    water_ml: float = 0
    caffeine_mg: float = 0
    notes: Optional[str] = None

class NutritionEntryCreate(NutritionEntryBase):
    pass

class NutritionEntry(NutritionEntryBase):
    id: int
    user_id: int
    consumed_at: datetime
    calories: Optional[float] = None
    protein: Optional[float] = None
    carbs: Optional[float] = None
    fat: Optional[float] = None
    fiber: Optional[float] = None
    sugar: Optional[float] = None
    sodium: Optional[float] = None
    
    class Config:
        from_attributes = True

# Meal schemas
class MealBase(BaseModel):
    name: str
    description: Optional[str] = None
    meal_type: Optional[str] = None
    is_template: bool = False

class MealCreate(MealBase):
    food_item_ids: List[int] = []

class Meal(MealBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Exercise schemas
class ExerciseTypeBase(BaseModel):
    name: str
    category: Optional[str] = None
    met_value: Optional[float] = None
    description: Optional[str] = None

class ExerciseType(ExerciseTypeBase):
    id: int
    
    class Config:
        from_attributes = True

class ExerciseEntryBase(BaseModel):
    exercise_type_id: Optional[int] = None
    duration_minutes: Optional[float] = None
    intensity: Optional[str] = None
    calories_burned: Optional[float] = None
    steps: Optional[int] = None
    distance_miles: Optional[float] = None
    heart_rate_avg: Optional[int] = None
    heart_rate_max: Optional[int] = None
    sets: Optional[int] = None
    reps: Optional[int] = None
    weight_lbs: Optional[float] = None
    notes: Optional[str] = None

class ExerciseEntryCreate(ExerciseEntryBase):
    pass

class ExerciseEntry(ExerciseEntryBase):
    id: int
    user_id: int
    performed_at: datetime
    
    class Config:
        from_attributes = True

# Analytics schemas
class DailyNutritionSummary(BaseModel):
    date: datetime
    total_calories: float
    total_protein: float
    total_carbs: float
    total_fat: float
    total_fiber: float
    total_sodium: float
    total_water_ml: float

class WeightTrend(BaseModel):
    date: datetime
    weight_lbs: float
    bmi: Optional[float] = None
