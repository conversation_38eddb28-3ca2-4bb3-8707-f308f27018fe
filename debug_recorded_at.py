#!/usr/bin/env python3
"""
Debug the recorded_at field issue
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models import User, BiometricEntry
from app.schemas import BiometricEntryCreate
from app.routers.biometrics import create_biometric_entry
from app.database import get_db
from fastapi import Depends

def debug_recorded_at():
    """Debug what's happening with the recorded_at field"""
    
    print("🔍 Debugging recorded_at Field Issue...")
    
    # Connect to existing database
    engine = create_engine("sqlite:///./weightloss_tracker.db")
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Get an existing user
        user = db.query(User).first()
        if not user:
            print("❌ No users found in database")
            return False
        
        print(f"👤 Using user: {user.username}")
        
        # Test 1: Check what the frontend would send
        print("\n1️⃣ Simulating frontend request...")
        
        # This is what the frontend should send
        custom_date = datetime.now() - timedelta(days=1)
        custom_date = custom_date.replace(hour=14, minute=30, second=0, microsecond=0)
        
        frontend_data = {
            "weight_lbs": 182.5,
            "body_fat_percentage": 16.8,
            "muscle_mass_lbs": 79.2,
            "water_percentage": 61.8,
            "recorded_at": custom_date.isoformat()
        }
        
        print(f"   Frontend would send: {frontend_data}")
        print(f"   recorded_at value: {frontend_data['recorded_at']}")
        print(f"   recorded_at type: {type(frontend_data['recorded_at'])}")
        
        # Test 2: Check schema parsing
        print("\n2️⃣ Testing schema parsing...")
        
        try:
            entry_schema = BiometricEntryCreate(**frontend_data)
            print(f"   Schema parsed recorded_at: {entry_schema.recorded_at}")
            print(f"   Schema recorded_at type: {type(entry_schema.recorded_at)}")
        except Exception as e:
            print(f"   ❌ Schema parsing error: {e}")
            return False
        
        # Test 3: Check database entry creation
        print("\n3️⃣ Testing database entry creation...")
        
        print(f"   Creating entry with recorded_at: {entry_schema.recorded_at}")
        
        db_entry = BiometricEntry(
            user_id=user.id,
            recorded_at=entry_schema.recorded_at,
            weight_lbs=entry_schema.weight_lbs,
            body_fat_percentage=entry_schema.body_fat_percentage,
            muscle_mass_lbs=entry_schema.muscle_mass_lbs,
            water_percentage=entry_schema.water_percentage
        )
        
        print(f"   DB entry recorded_at before save: {db_entry.recorded_at}")
        
        db.add(db_entry)
        db.commit()
        db.refresh(db_entry)
        
        print(f"   DB entry recorded_at after save: {db_entry.recorded_at}")
        print(f"   Entry ID: {db_entry.id}")
        
        # Test 4: Check what gets returned to frontend
        print("\n4️⃣ Checking what gets returned...")
        
        # Simulate what the API would return
        returned_data = {
            "id": db_entry.id,
            "user_id": db_entry.user_id,
            "recorded_at": db_entry.recorded_at,
            "weight_lbs": db_entry.weight_lbs,
            "body_fat_percentage": db_entry.body_fat_percentage,
            "muscle_mass_lbs": db_entry.muscle_mass_lbs,
            "water_percentage": db_entry.water_percentage,
            "bmr_calculated": db_entry.bmr_calculated,
            "bmr_predicted": db_entry.bmr_predicted
        }
        
        print(f"   API would return: {returned_data['recorded_at']}")
        
        # Test 5: Check recent entries to see the pattern
        print("\n5️⃣ Checking recent entries pattern...")
        
        recent_entries = db.query(BiometricEntry).filter(
            BiometricEntry.user_id == user.id
        ).order_by(BiometricEntry.recorded_at.desc()).limit(3).all()
        
        print("   Recent entries:")
        for i, entry in enumerate(recent_entries):
            print(f"     {i+1}. ID: {entry.id}, recorded_at: {entry.recorded_at}, weight: {entry.weight_lbs}")
        
        # Test 6: Check if there's a database default override
        print("\n6️⃣ Checking database model...")
        
        # Look at the BiometricEntry model
        from app.models import BiometricEntry as ModelClass
        import inspect
        
        # Get the recorded_at column definition
        recorded_at_column = ModelClass.__table__.columns['recorded_at']
        print(f"   recorded_at column: {recorded_at_column}")
        print(f"   column type: {recorded_at_column.type}")
        print(f"   column default: {recorded_at_column.default}")
        print(f"   column server_default: {recorded_at_column.server_default}")
        
        # Test 7: Try creating without recorded_at to see default behavior
        print("\n7️⃣ Testing default behavior...")
        
        db_entry_default = BiometricEntry(
            user_id=user.id,
            weight_lbs=181.0,
            body_fat_percentage=16.5
            # No recorded_at specified
        )
        
        print(f"   Entry without recorded_at before save: {db_entry_default.recorded_at}")
        
        db.add(db_entry_default)
        db.commit()
        db.refresh(db_entry_default)
        
        print(f"   Entry without recorded_at after save: {db_entry_default.recorded_at}")
        
        # Summary
        print("\n📋 Debug Summary:")
        print(f"   Custom timestamp sent: {custom_date}")
        print(f"   Schema parsed timestamp: {entry_schema.recorded_at}")
        print(f"   Database stored timestamp: {db_entry.recorded_at}")
        print(f"   Match: {db_entry.recorded_at == entry_schema.recorded_at}")
        
        if db_entry.recorded_at == entry_schema.recorded_at:
            print("\n✅ The recorded_at field is working correctly!")
            print("   The issue might be in the frontend display or data flow.")
        else:
            print("\n❌ There's an issue with recorded_at storage!")
            print("   The database is not storing the custom timestamp.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("Debug recorded_at Field")
    print("=" * 40)
    debug_recorded_at()
