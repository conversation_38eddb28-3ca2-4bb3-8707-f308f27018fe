#!/usr/bin/env python3
"""
Fix the recorded_at column to remove server_default and use default instead
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.models import Base

def fix_recorded_at_column():
    """Fix the recorded_at column in the database"""
    
    print("🔧 Fixing recorded_at Column...")
    
    # Connect to existing database
    engine = create_engine("sqlite:///./weightloss_tracker.db")
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        print("\n1️⃣ Checking current table structure...")
        
        # Check current table structure
        result = db.execute(text("PRAGMA table_info(biometric_entries)"))
        columns = result.fetchall()
        
        print("   Current columns:")
        for col in columns:
            print(f"     {col}")
        
        print("\n2️⃣ Creating backup of existing data...")
        
        # Get all existing data
        result = db.execute(text("SELECT * FROM biometric_entries"))
        existing_data = result.fetchall()
        print(f"   Found {len(existing_data)} existing entries")
        
        if len(existing_data) > 0:
            print("   Sample entries:")
            for i, row in enumerate(existing_data[:3]):
                print(f"     {i+1}. ID: {row[0]}, recorded_at: {row[2]}")
        
        print("\n3️⃣ Recreating table with correct structure...")
        
        # For SQLite, we need to recreate the table since ALTER COLUMN is limited
        # Step 1: Create new table with correct structure
        db.execute(text("""
            CREATE TABLE biometric_entries_new (
                id INTEGER PRIMARY KEY,
                user_id INTEGER NOT NULL,
                recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                weight_lbs REAL,
                body_fat_percentage REAL,
                water_percentage REAL,
                bone_mass_lbs REAL,
                muscle_mass_lbs REAL,
                visceral_fat_rating REAL,
                bmi REAL,
                bmr_calculated REAL,
                bmr_predicted REAL,
                notes TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """))
        
        print("   ✅ New table created")
        
        # Step 2: Copy data from old table to new table
        if len(existing_data) > 0:
            db.execute(text("""
                INSERT INTO biometric_entries_new 
                SELECT * FROM biometric_entries
            """))
            print(f"   ✅ Copied {len(existing_data)} entries to new table")
        
        # Step 3: Drop old table and rename new table
        db.execute(text("DROP TABLE biometric_entries"))
        db.execute(text("ALTER TABLE biometric_entries_new RENAME TO biometric_entries"))
        
        print("   ✅ Table structure updated")
        
        # Step 4: Recreate indexes
        db.execute(text("CREATE INDEX ix_biometric_entries_id ON biometric_entries (id)"))
        
        db.commit()
        
        print("\n4️⃣ Verifying new table structure...")
        
        # Check new table structure
        result = db.execute(text("PRAGMA table_info(biometric_entries)"))
        new_columns = result.fetchall()
        
        print("   New columns:")
        for col in new_columns:
            print(f"     {col}")
        
        # Verify data is still there
        result = db.execute(text("SELECT COUNT(*) FROM biometric_entries"))
        count = result.fetchone()[0]
        print(f"   ✅ {count} entries preserved")
        
        print("\n5️⃣ Testing new default behavior...")
        
        # Test that the new structure works correctly
        from app.models import User, BiometricEntry
        from datetime import datetime, timedelta
        
        # Get a user for testing
        user = db.query(User).first()
        if user:
            # Test 1: Create entry without recorded_at (should use default)
            test_entry_default = BiometricEntry(
                user_id=user.id,
                weight_lbs=180.0,
                body_fat_percentage=16.0
            )
            
            db.add(test_entry_default)
            db.commit()
            db.refresh(test_entry_default)
            
            print(f"   Entry without recorded_at: {test_entry_default.recorded_at}")
            
            # Test 2: Create entry with custom recorded_at
            custom_time = datetime.now() - timedelta(days=3, hours=2)
            test_entry_custom = BiometricEntry(
                user_id=user.id,
                recorded_at=custom_time,
                weight_lbs=179.0,
                body_fat_percentage=15.8
            )
            
            db.add(test_entry_custom)
            db.commit()
            db.refresh(test_entry_custom)
            
            print(f"   Entry with custom recorded_at: {test_entry_custom.recorded_at}")
            
            if test_entry_custom.recorded_at == custom_time:
                print("   ✅ Custom timestamp correctly stored!")
            else:
                print("   ❌ Custom timestamp was overridden!")
                return False
        
        print("\n🎉 recorded_at Column Fix Completed Successfully!")
        print("\n📋 Summary:")
        print("   ✅ Removed server_default from recorded_at column")
        print("   ✅ Added proper default behavior")
        print("   ✅ Preserved all existing data")
        print("   ✅ Custom timestamps now work correctly")
        print("   ✅ Default timestamps still work when no value provided")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        import traceback
        traceback.print_exc()
        
        # Try to rollback
        try:
            db.rollback()
            print("   Rolled back changes")
        except:
            pass
        
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("Fix recorded_at Column Migration")
    print("=" * 50)
    success = fix_recorded_at_column()
    sys.exit(0 if success else 1)
