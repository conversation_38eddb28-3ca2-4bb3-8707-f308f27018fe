"""
Weight Loss Tracker - Streamlit Frontend Application
"""
import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, date, timedelta
import json

def format_height(height_inches):
    """Format height in feet and inches"""
    if height_inches:
        feet = int(height_inches // 12)
        inches = height_inches % 12
        return f"{feet}'{inches:.1f}\""
    return "Not set"

def format_weight(weight_lbs):
    """Format weight in pounds"""
    if weight_lbs:
        return f"{weight_lbs:.1f} lbs"
    return "Not set"

# Configuration
API_BASE_URL = "http://localhost:8000/api"

# Page configuration
st.set_page_config(
    page_title="Weight Loss Tracker",
    page_icon="⚖️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .success-message {
        color: #28a745;
        font-weight: bold;
    }
    .error-message {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

# Session state initialization
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = False
if 'access_token' not in st.session_state:
    st.session_state.access_token = None
if 'user_data' not in st.session_state:
    st.session_state.user_data = None

def make_authenticated_request(method, endpoint, data=None, params=None):
    """Make an authenticated API request"""
    headers = {}
    if st.session_state.access_token:
        headers["Authorization"] = f"Bearer {st.session_state.access_token}"
    
    url = f"{API_BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        
        if response.status_code == 401:
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.error("Session expired. Please log in again.")
            return None
        
        return response
    except requests.exceptions.ConnectionError:
        st.error("Cannot connect to the API server. Please ensure the backend is running.")
        return None

def login_page():
    """Login page"""
    st.markdown('<h1 class="main-header">⚖️ Weight Loss Tracker</h1>', unsafe_allow_html=True)
    
    tab1, tab2 = st.tabs(["Login", "Register"])
    
    with tab1:
        st.subheader("Login")

        # Quick demo login button
        if st.button("🚀 Use Demo Account", type="primary"):
            try:
                response = requests.post(
                    f"{API_BASE_URL}/auth/login",
                    data={"username": "demo", "password": "demo123"}
                )

                if response.status_code == 200:
                    token_data = response.json()
                    st.session_state.access_token = token_data["access_token"]
                    st.session_state.authenticated = True
                    st.success("Demo login successful!")
                    st.rerun()
                else:
                    st.error("Demo account not available")
            except requests.exceptions.ConnectionError:
                st.error("Cannot connect to the API server. Please ensure the backend is running.")

        st.divider()

        with st.form("login_form"):
            username = st.text_input("Username", placeholder="Enter your username")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            submit = st.form_submit_button("Login")

            if submit:
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/auth/login",
                        data={"username": username, "password": password}
                    )

                    if response.status_code == 200:
                        token_data = response.json()
                        st.session_state.access_token = token_data["access_token"]
                        st.session_state.authenticated = True
                        st.success("Login successful!")
                        st.rerun()
                    else:
                        st.error("Invalid credentials")
                except requests.exceptions.ConnectionError:
                    st.error("Cannot connect to the API server. Please ensure the backend is running.")
    
    with tab2:
        st.subheader("Quick Setup")
        st.info("💡 For local use - just set a username, password, and basic info to get started! All measurements use US units (lbs, inches, miles).")
        with st.form("register_form"):
            username = st.text_input("Username", value="user1")
            password = st.text_input("Password", type="password", value="password123")

            col1, col2 = st.columns(2)
            with col1:
                height_feet = st.number_input("Height (feet)", min_value=3, max_value=8, value=5)
                height_inches = st.number_input("Height (inches)", min_value=0.0, max_value=11.9, value=8.0, step=0.1)
                total_height_inches = height_feet * 12 + height_inches
                birth_date = st.date_input("Date of Birth", value=date(1990, 1, 1), max_value=date.today())
            with col2:
                goal_weight_lbs = st.number_input("Goal Weight (lbs)", min_value=70.0, max_value=500.0, value=150.0)
                activity_level = st.selectbox("Activity Level", [
                    "lightly_active", "moderately_active", "very_active", "sedentary", "extra_active"
                ])
                goal_type = st.selectbox("Goal Type", ["lose_weight", "maintain_weight", "gain_weight"])

            submit = st.form_submit_button("Create Account")

            if submit:
                # Generate a simple email for local use
                email = f"{username}@local.app"
                user_data = {
                    "email": email,
                    "username": username,
                    "password": password,
                    "first_name": "User",
                    "last_name": "Local",
                    "date_of_birth": birth_date.isoformat(),
                    "gender": "other",
                    "height_inches": total_height_inches,
                    "activity_level": activity_level,
                    "goal_weight_lbs": goal_weight_lbs,
                    "goal_type": goal_type
                }
                
                try:
                    response = requests.post(f"{API_BASE_URL}/auth/register", json=user_data)
                    
                    if response.status_code == 200:
                        st.success("Registration successful! Please log in.")
                    else:
                        error_detail = response.json().get("detail", "Registration failed")
                        st.error(f"Registration failed: {error_detail}")
                except requests.exceptions.ConnectionError:
                    st.error("Cannot connect to the API server. Please ensure the backend is running.")

def dashboard_page():
    """Main dashboard page"""
    st.markdown('<h1 class="main-header">📊 Dashboard</h1>', unsafe_allow_html=True)
    
    # Get user data
    if not st.session_state.user_data:
        response = make_authenticated_request("GET", "/auth/me")
        if response and response.status_code == 200:
            st.session_state.user_data = response.json()
    
    # Get progress summary
    response = make_authenticated_request("GET", "/analytics/progress-summary")
    if response and response.status_code == 200:
        progress_data = response.json()
        
        # Display key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            current_weight = progress_data.get("current_weight_lbs")
            if current_weight:
                st.metric("Current Weight", f"{current_weight:.1f} lbs")
            else:
                st.metric("Current Weight", "No data")

        with col2:
            weight_change = progress_data.get("weight_change_lbs")
            if weight_change is not None:
                st.metric("Weight Change", f"{weight_change:+.1f} lbs", delta=f"{weight_change:+.1f}")
            else:
                st.metric("Weight Change", "No data")

        with col3:
            current_bmi = progress_data.get("current_bmi")
            if current_bmi:
                st.metric("Current BMI", f"{current_bmi:.1f}")
            else:
                st.metric("Current BMI", "No data")

        with col4:
            goal_weight = progress_data.get("goal_weight_lbs")
            if goal_weight:
                st.metric("Goal Weight", f"{goal_weight:.1f} lbs")
            else:
                st.metric("Goal Weight", "Not set")
    
    # Charts section
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Weight Trend")
        response = make_authenticated_request("GET", "/analytics/weight-trend", params={"days": 30})
        if response and response.status_code == 200:
            weight_data = response.json()
            if weight_data:
                df = pd.DataFrame(weight_data)
                df['date'] = pd.to_datetime(df['date'], format='ISO8601')
                
                fig = px.line(df, x='date', y='weight_lbs', title='Weight Trend (30 days)')
                fig.update_layout(xaxis_title="Date", yaxis_title="Weight (lbs)")
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No weight data available")
    
    with col2:
        st.subheader("Today's Macro Breakdown")
        response = make_authenticated_request("GET", "/analytics/macro-breakdown")
        if response and response.status_code == 200:
            macro_data = response.json()
            if macro_data["total_calories"] > 0:
                labels = ['Protein', 'Carbs', 'Fat']
                values = [
                    macro_data["protein_percentage"],
                    macro_data["carbs_percentage"],
                    macro_data["fat_percentage"]
                ]
                
                fig = px.pie(values=values, names=labels, title='Macro Distribution')
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No nutrition data for today")

def biometrics_page():
    """Biometrics tracking page"""
    st.markdown('<h1 class="main-header">📏 Biometrics</h1>', unsafe_allow_html=True)
    
    tab1, tab2, tab3 = st.tabs(["Add Entry", "View History", "BMR Insights"])
    
    with tab1:
        st.subheader("Add Biometric Entry")
        st.info("📏 Enter your measurements in US units (pounds, inches)")
        with st.form("biometric_form"):
            col1, col2, col3 = st.columns(3)

            with col1:
                weight_lbs = st.number_input("Weight (lbs)", min_value=70.0, max_value=500.0, step=0.1)
                body_fat_percentage = st.number_input("Body Fat %", min_value=0.0, max_value=50.0, step=0.1)
                water_percentage = st.number_input("Water %", min_value=0.0, max_value=100.0, step=0.1)

            with col2:
                muscle_mass_lbs = st.number_input("Muscle Mass (lbs)", min_value=0.0, max_value=200.0, step=0.1)
                bone_mass_lbs = st.number_input("Bone Mass (lbs)", min_value=0.0, max_value=20.0, step=0.1)
                visceral_fat_rating = st.number_input("Visceral Fat Rating", min_value=0.0, max_value=30.0, step=0.1)

            with col3:
                recorded_date = st.date_input("Date recorded", value=date.today())
                recorded_time = st.time_input("Time recorded", value=datetime.now().time())
                notes = st.text_area("Notes", key="biometric_notes")
            
            submit = st.form_submit_button("Add Entry")
            
            if submit:
                # Combine date and time
                recorded_datetime = datetime.combine(recorded_date, recorded_time)

                entry_data = {
                    "weight_lbs": weight_lbs if weight_lbs > 0 else None,
                    "body_fat_percentage": body_fat_percentage if body_fat_percentage > 0 else None,
                    "water_percentage": water_percentage if water_percentage > 0 else None,
                    "bone_mass_lbs": bone_mass_lbs if bone_mass_lbs > 0 else None,
                    "muscle_mass_lbs": muscle_mass_lbs if muscle_mass_lbs > 0 else None,
                    "visceral_fat_rating": visceral_fat_rating if visceral_fat_rating > 0 else None,
                    "notes": notes if notes else None,
                    "recorded_at": recorded_datetime.isoformat()
                }
                
                response = make_authenticated_request("POST", "/biometrics/", data=entry_data)
                if response and response.status_code == 200:
                    st.success("Biometric entry added successfully!")
                    st.rerun()
                else:
                    st.error("Failed to add biometric entry")
    
    with tab2:
        st.subheader("Biometric History")
        response = make_authenticated_request("GET", "/biometrics/", params={"limit": 50})
        if response and response.status_code == 200:
            entries = response.json()
            if entries:
                df = pd.DataFrame(entries)
                df['recorded_at'] = pd.to_datetime(df['recorded_at'], format='ISO8601').dt.strftime('%Y-%m-%d %H:%M')
                
                # Display table with edit/delete options
                st.subheader("📊 Entry Management")

                # Select entry to edit/delete
                entry_options = [f"{row['recorded_at']} - {row['weight_lbs']:.1f} lbs" for _, row in df.iterrows()]
                selected_entry = st.selectbox("Select entry to edit/delete:", [""] + entry_options)

                if selected_entry:
                    entry_index = entry_options.index(selected_entry)
                    entry_data = entries[entry_index]

                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("✏️ Edit Entry", key=f"edit_{entry_data['id']}"):
                            st.session_state.editing_entry = entry_data
                    with col2:
                        if st.button("🗑️ Delete Entry", key=f"delete_{entry_data['id']}"):
                            delete_response = make_authenticated_request("DELETE", f"/biometrics/{entry_data['id']}")
                            if delete_response and delete_response.status_code == 200:
                                st.success("Entry deleted successfully!")
                                st.rerun()
                            else:
                                st.error("Failed to delete entry")

                # Edit form
                if 'editing_entry' in st.session_state:
                    st.subheader("✏️ Edit Biometric Entry")
                    edit_entry = st.session_state.editing_entry

                    with st.form("edit_biometric_form"):
                        col1, col2 = st.columns(2)
                        with col1:
                            edit_weight = st.number_input("Weight (lbs)", value=float(edit_entry.get('weight_lbs', 0)), min_value=70.0, max_value=500.0, step=0.1)
                            edit_body_fat = st.number_input("Body Fat %", value=float(edit_entry.get('body_fat_percentage', 0)), min_value=0.0, max_value=50.0, step=0.1)
                            edit_water = st.number_input("Water %", value=float(edit_entry.get('water_percentage', 0)), min_value=0.0, max_value=100.0, step=0.1)
                        with col2:
                            edit_muscle = st.number_input("Muscle Mass (lbs)", value=float(edit_entry.get('muscle_mass_lbs', 0)), min_value=0.0, max_value=200.0, step=0.1)
                            edit_bone = st.number_input("Bone Mass (lbs)", value=float(edit_entry.get('bone_mass_lbs', 0)), min_value=0.0, max_value=20.0, step=0.1)
                            edit_visceral = st.number_input("Visceral Fat Rating", value=float(edit_entry.get('visceral_fat_rating', 0)), min_value=0.0, max_value=30.0, step=0.1)

                        edit_notes = st.text_area("Notes", value=edit_entry.get('notes', ''), key="edit_biometric_notes")

                        # Date and time editing
                        col3, col4 = st.columns(2)
                        with col3:
                            # Parse existing recorded_at
                            existing_datetime = pd.to_datetime(edit_entry.get('recorded_at'))
                            edit_date = st.date_input("Date recorded", value=existing_datetime.date())
                        with col4:
                            edit_time = st.time_input("Time recorded", value=existing_datetime.time())

                        col1, col2 = st.columns(2)
                        with col1:
                            if st.form_submit_button("💾 Save Changes"):
                                # Combine edited date and time
                                edit_datetime = datetime.combine(edit_date, edit_time)

                                update_data = {
                                    "weight_lbs": edit_weight if edit_weight > 0 else None,
                                    "body_fat_percentage": edit_body_fat if edit_body_fat > 0 else None,
                                    "water_percentage": edit_water if edit_water > 0 else None,
                                    "muscle_mass_lbs": edit_muscle if edit_muscle > 0 else None,
                                    "bone_mass_lbs": edit_bone if edit_bone > 0 else None,
                                    "visceral_fat_rating": edit_visceral if edit_visceral > 0 else None,
                                    "notes": edit_notes if edit_notes else None,
                                    "recorded_at": edit_datetime.isoformat()
                                }

                                update_response = make_authenticated_request("PUT", f"/biometrics/{edit_entry['id']}", data=update_data)
                                if update_response and update_response.status_code == 200:
                                    st.success("Entry updated successfully!")
                                    del st.session_state.editing_entry
                                    st.rerun()
                                else:
                                    st.error("Failed to update entry")
                        with col2:
                            if st.form_submit_button("❌ Cancel"):
                                del st.session_state.editing_entry
                                st.rerun()

                st.subheader("📋 All Entries")
                # Show available BMR columns
                bmr_display_cols = ['recorded_at', 'weight_lbs', 'body_fat_percentage', 'bmi', 'bmr_calculated']
                if 'bmr_predicted' in df.columns:
                    bmr_display_cols.append('bmr_predicted')
                available_display_cols = [col for col in bmr_display_cols if col in df.columns]
                st.dataframe(df[available_display_cols], use_container_width=True)
                
                # Biometric trends chart
                st.subheader("Biometric Trends")
                metrics = st.multiselect(
                    "Select metrics to display",
                    ['weight_lbs', 'body_fat_percentage', 'muscle_mass_lbs', 'water_percentage'],
                    default=['weight_lbs', 'body_fat_percentage']
                )
                
                if metrics:
                    response = make_authenticated_request("GET", "/analytics/biometric-trends", 
                                                        params={"metrics": metrics, "days": 60})
                    if response and response.status_code == 200:
                        trends_data = response.json()
                        
                        fig = make_subplots(specs=[[{"secondary_y": True}]])
                        
                        for i, metric in enumerate(metrics):
                            if metric in trends_data and trends_data[metric]:
                                df_metric = pd.DataFrame(trends_data[metric])
                                df_metric['date'] = pd.to_datetime(df_metric['date'], format='ISO8601')
                                
                                fig.add_trace(
                                    go.Scatter(x=df_metric['date'], y=df_metric['value'], 
                                             name=metric.replace('_', ' ').title()),
                                    secondary_y=(i > 0)
                                )
                        
                        fig.update_layout(title="Biometric Trends")
                        st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No biometric entries found")

    with tab3:
        st.subheader("🧠 BMR Insights & ML Predictions")
        st.info("🔬 Advanced BMR analysis using machine learning to track metabolic adaptations")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔄 Refresh BMR Insights"):
                st.rerun()

        with col2:
            if st.button("🎯 Train BMR Model"):
                with st.spinner("Training BMR prediction model..."):
                    train_response = make_authenticated_request("POST", "/biometrics/train-bmr-model")
                    if train_response and train_response.status_code == 200:
                        result = train_response.json()
                        if result.get("success"):
                            st.success(f"✅ Model trained successfully!")
                            st.json(result)
                        else:
                            st.warning(f"⚠️ Training failed: {result.get('message', 'Unknown error')}")
                    else:
                        st.error("❌ Failed to train model")

        # Get BMR insights
        insights_response = make_authenticated_request("GET", "/biometrics/bmr-insights")
        if insights_response and insights_response.status_code == 200:
            insights = insights_response.json()

            if "message" in insights:
                st.info(insights["message"])
            else:
                # Display current BMR
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric(
                        "Current BMR",
                        f"{insights.get('current_bmr', 0):.0f} cal/day",
                        delta=f"{insights.get('bmr_change', 0):+.0f} cal"
                    )

                with col2:
                    trend = insights.get('bmr_trend', 'unknown')
                    trend_emoji = {"increasing": "📈", "decreasing": "📉", "stable": "➡️"}.get(trend, "❓")
                    st.metric(
                        "BMR Trend",
                        f"{trend_emoji} {trend.title()}",
                        delta=f"{insights.get('weight_change_lbs', 0):+.1f} lbs"
                    )

                with col3:
                    st.metric(
                        "Analysis Period",
                        f"{insights.get('analysis_period_days', 0)} days"
                    )

                # Recommendations
                if insights.get('recommendations'):
                    st.subheader("💡 Recommendations")
                    for rec in insights['recommendations']:
                        st.info(f"💡 {rec}")

                # Display detailed insights
                st.subheader("📊 Detailed Analysis")

                # Get recent biometric entries for BMR comparison
                response = make_authenticated_request("GET", "/biometrics/", params={"limit": 10})
                if response and response.status_code == 200:
                    entries = response.json()
                    if entries:
                        df = pd.DataFrame(entries)
                        df['recorded_at'] = pd.to_datetime(df['recorded_at'])

                        # Create BMR comparison chart
                        if 'bmr_calculated' in df.columns and 'bmr_predicted' in df.columns:
                            fig = go.Figure()

                            # Add calculated BMR
                            fig.add_trace(go.Scatter(
                                x=df['recorded_at'],
                                y=df['bmr_calculated'],
                                mode='lines+markers',
                                name='Standard BMR',
                                line=dict(color='blue')
                            ))

                            # Add predicted BMR if available
                            predicted_data = df[df['bmr_predicted'].notna()]
                            if not predicted_data.empty:
                                fig.add_trace(go.Scatter(
                                    x=predicted_data['recorded_at'],
                                    y=predicted_data['bmr_predicted'],
                                    mode='lines+markers',
                                    name='ML-Predicted BMR',
                                    line=dict(color='red', dash='dash')
                                ))

                            fig.update_layout(
                                title="BMR Comparison: Standard vs ML Prediction",
                                xaxis_title="Date",
                                yaxis_title="BMR (calories/day)",
                                hovermode='x unified'
                            )

                            st.plotly_chart(fig, use_container_width=True)

                        # Show BMR data table
                        st.subheader("📋 Recent BMR Data")
                        bmr_cols = ['recorded_at', 'weight_lbs', 'bmr_calculated', 'bmr_predicted']
                        available_cols = [col for col in bmr_cols if col in df.columns]
                        if available_cols:
                            display_df = df[available_cols].copy()
                            display_df['recorded_at'] = display_df['recorded_at'].dt.strftime('%Y-%m-%d %H:%M')
                            st.dataframe(display_df, use_container_width=True)
        else:
            st.error("❌ Failed to load BMR insights")

def nutrition_page():
    """Nutrition tracking page"""
    st.markdown('<h1 class="main-header">🍎 Nutrition Tracking</h1>', unsafe_allow_html=True)

    tab1, tab2, tab3 = st.tabs(["Add Food Entry", "Food Database", "Entry History"])

    with tab1:
        st.subheader("Log Food Consumption")
        st.info("🍽️ Track your daily food intake with automatic macro calculations")

        # Search for food
        search_query = st.text_input("🔍 Search for food:", placeholder="e.g., chicken breast, banana, oatmeal")

        if search_query:
            response = make_authenticated_request("GET", "/nutrition/foods", params={"q": search_query, "limit": 10})
            if response and response.status_code == 200:
                foods = response.json()
                if foods:
                    food_options = [f"{food['name']} ({food['brand'] or 'Generic'})" for food in foods]
                    selected_food = st.selectbox("Select food:", food_options)

                    if selected_food:
                        food_index = food_options.index(selected_food)
                        selected_food_data = foods[food_index]

                        # Calculate nutrition per serving
                        serving_size = selected_food_data.get('serving_size_g', 100)
                        serving_desc = selected_food_data.get('serving_description', f"{serving_size}g")

                        calories_per_serving = (selected_food_data['calories_per_100g'] * serving_size) / 100
                        protein_per_serving = (selected_food_data['protein_per_100g'] * serving_size) / 100
                        carbs_per_serving = (selected_food_data['carbs_per_100g'] * serving_size) / 100
                        fat_per_serving = (selected_food_data['fat_per_100g'] * serving_size) / 100

                        # Display nutrition info
                        st.subheader(f"📊 Nutrition Information (per serving: {serving_desc})")
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("Calories", f"{calories_per_serving:.0f}")
                        with col2:
                            st.metric("Protein", f"{protein_per_serving:.1f}g")
                        with col3:
                            st.metric("Carbs", f"{carbs_per_serving:.1f}g")
                        with col4:
                            st.metric("Fat", f"{fat_per_serving:.1f}g")

                        # Entry form
                        with st.form("nutrition_entry_form"):
                            col1, col2 = st.columns(2)
                            with col1:
                                quantity = st.number_input("Quantity", min_value=0.1, value=1.0, step=0.1, key="nutrition_quantity")
                                unit = st.selectbox("Unit", ["serving", "g", "ml", "oz", "cup"], index=0)
                                consumed_date = st.date_input("Date consumed", value=date.today())
                            with col2:
                                consumed_time = st.time_input("Time consumed", value=datetime.now().time())
                                water_ml = st.number_input("Water content (ml)", min_value=0.0, value=0.0, step=1.0)
                                notes = st.text_input("Notes (optional)", key="nutrition_notes")

                            if st.form_submit_button("📝 Log Food Entry"):
                                # Combine date and time
                                consumed_datetime = datetime.combine(consumed_date, consumed_time)

                                entry_data = {
                                    "food_item_id": selected_food_data['id'],
                                    "quantity": quantity,
                                    "unit": unit,
                                    "water_ml": water_ml,
                                    "notes": notes,
                                    "consumed_at": consumed_datetime.isoformat()
                                }

                                response = make_authenticated_request("POST", "/nutrition/entries", data=entry_data)
                                if response and response.status_code == 200:
                                    st.success("Food entry logged successfully!")
                                    st.rerun()
                                else:
                                    st.error("Failed to log food entry")
                else:
                    st.info("No foods found. Try a different search term.")

    with tab2:
        st.subheader("Food Database")
        st.info("🗃️ Browse and add foods to the database")

        # Add new food
        with st.expander("➕ Add New Food to Database"):
            with st.form("add_food_form"):
                col1, col2 = st.columns(2)
                with col1:
                    food_name = st.text_input("Food Name*")
                    brand = st.text_input("Brand (optional)")
                    serving_size = st.number_input("Serving size (g)", min_value=1.0, value=100.0, step=1.0)
                    serving_desc = st.text_input("Serving description", placeholder="e.g., 1 medium, 1 cup, 1 slice")
                with col2:
                    st.info("📊 Enter nutrition facts per serving")
                    calories_per_serving = st.number_input("Calories per serving*", min_value=0.0, step=1.0)
                    protein_per_serving = st.number_input("Protein per serving (g)", min_value=0.0, step=0.1)

                col1, col2 = st.columns(2)
                with col1:
                    carbs_per_serving = st.number_input("Carbs per serving (g)", min_value=0.0, step=0.1)
                    fat_per_serving = st.number_input("Fat per serving (g)", min_value=0.0, step=0.1)
                with col2:
                    fiber_per_serving = st.number_input("Fiber per serving (g)", min_value=0.0, step=0.1)
                    sodium_per_serving = st.number_input("Sodium per serving (mg)", min_value=0.0, step=1.0)

                if st.form_submit_button("💾 Add Food"):
                    if food_name and calories_per_serving > 0:
                        # Convert per-serving values to per-100g for database storage
                        calories_per_100g = (calories_per_serving * 100) / serving_size
                        protein_per_100g = (protein_per_serving * 100) / serving_size
                        carbs_per_100g = (carbs_per_serving * 100) / serving_size
                        fat_per_100g = (fat_per_serving * 100) / serving_size
                        fiber_per_100g = (fiber_per_serving * 100) / serving_size
                        sodium_per_100g = (sodium_per_serving * 100) / serving_size

                        food_data = {
                            "name": food_name,
                            "brand": brand,
                            "calories_per_100g": calories_per_100g,
                            "protein_per_100g": protein_per_100g,
                            "carbs_per_100g": carbs_per_100g,
                            "fat_per_100g": fat_per_100g,
                            "fiber_per_100g": fiber_per_100g,
                            "sodium_per_100g": sodium_per_100g,
                            "serving_size_g": serving_size,
                            "serving_description": serving_desc
                        }

                        response = make_authenticated_request("POST", "/nutrition/foods", data=food_data)
                        if response and response.status_code == 200:
                            st.success("Food added to database!")
                            st.info(f"Added {food_name}: {calories_per_serving:.0f} cal per {serving_desc or f'{serving_size}g'}")
                        else:
                            st.error("Failed to add food")
                    else:
                        st.error("Please fill in required fields (name and calories)")

        # Browse foods
        st.subheader("Browse Foods")
        response = make_authenticated_request("GET", "/nutrition/foods", params={"limit": 20})
        if response and response.status_code == 200:
            foods = response.json()
            if foods:
                for food in foods:
                    with st.expander(f"{food['name']} ({food['brand'] or 'Generic'})"):
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.write(f"**Calories:** {food['calories_per_100g']:.0f}")
                        with col2:
                            st.write(f"**Protein:** {food['protein_per_100g']:.1f}g")
                        with col3:
                            st.write(f"**Carbs:** {food['carbs_per_100g']:.1f}g")
                        with col4:
                            st.write(f"**Fat:** {food['fat_per_100g']:.1f}g")

    with tab3:
        st.subheader("Nutrition Entry History")

        # Date filter
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("From date", value=date.today() - timedelta(days=7))
        with col2:
            end_date = st.date_input("To date", value=date.today())

        # Get entries
        params = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "limit": 100
        }
        response = make_authenticated_request("GET", "/nutrition/entries", params=params)

        if response and response.status_code == 200:
            entries = response.json()
            if entries:
                df = pd.DataFrame(entries)
                df['consumed_at'] = pd.to_datetime(df['consumed_at'], format='ISO8601').dt.strftime('%Y-%m-%d %H:%M')

                # Entry management
                st.subheader("📊 Entry Management")
                entry_options = [f"{row['consumed_at']} - {row.get('calories', 0):.0f} cal" for _, row in df.iterrows()]
                selected_entry = st.selectbox("Select entry to edit/delete:", [""] + entry_options)

                if selected_entry:
                    entry_index = entry_options.index(selected_entry)
                    entry_data = entries[entry_index]

                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("✏️ Edit Entry", key=f"edit_nutrition_{entry_data['id']}"):
                            st.session_state.editing_nutrition = entry_data
                    with col2:
                        if st.button("🗑️ Delete Entry", key=f"delete_nutrition_{entry_data['id']}"):
                            delete_response = make_authenticated_request("DELETE", f"/nutrition/entries/{entry_data['id']}")
                            if delete_response and delete_response.status_code == 200:
                                st.success("Entry deleted successfully!")
                                st.rerun()
                            else:
                                st.error("Failed to delete entry")

                # Edit form
                if 'editing_nutrition' in st.session_state:
                    st.subheader("✏️ Edit Nutrition Entry")
                    edit_entry = st.session_state.editing_nutrition

                    with st.form("edit_nutrition_form"):
                        col1, col2 = st.columns(2)
                        with col1:
                            edit_quantity = st.number_input("Quantity", value=float(edit_entry.get('quantity', 0)), min_value=0.1, step=0.1, key="edit_nutrition_quantity")
                            edit_unit = st.selectbox("Unit", ["g", "serving", "ml", "oz", "cup"],
                                                   index=["g", "serving", "ml", "oz", "cup"].index(edit_entry.get('unit', 'g')))
                        with col2:
                            edit_water = st.number_input("Water (ml)", value=float(edit_entry.get('water_ml', 0)), min_value=0.0, step=1.0)
                            edit_notes = st.text_input("Notes", value=edit_entry.get('notes', ''), key="edit_nutrition_notes")

                        col1, col2 = st.columns(2)
                        with col1:
                            if st.form_submit_button("💾 Save Changes"):
                                update_data = {
                                    "quantity": edit_quantity,
                                    "unit": edit_unit,
                                    "water_ml": edit_water,
                                    "notes": edit_notes
                                }

                                update_response = make_authenticated_request("PUT", f"/nutrition/entries/{edit_entry['id']}", data=update_data)
                                if update_response and update_response.status_code == 200:
                                    st.success("Entry updated successfully!")
                                    del st.session_state.editing_nutrition
                                    st.rerun()
                                else:
                                    st.error("Failed to update entry")
                        with col2:
                            if st.form_submit_button("❌ Cancel"):
                                del st.session_state.editing_nutrition
                                st.rerun()

                # Display entries
                st.subheader("📋 All Entries")
                display_cols = ['consumed_at', 'calories', 'protein', 'carbs', 'fat', 'quantity', 'unit']
                available_cols = [col for col in display_cols if col in df.columns]
                st.dataframe(df[available_cols], use_container_width=True)

                # Daily summary
                st.subheader("📊 Daily Summary")
                response = make_authenticated_request("GET", "/nutrition/entries/daily-summary",
                                                   params={"target_date": date.today().isoformat()})
                if response and response.status_code == 200:
                    summary = response.json()
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("Total Calories", f"{summary['total_calories']:.0f}")
                    with col2:
                        st.metric("Protein", f"{summary['total_protein']:.1f}g")
                    with col3:
                        st.metric("Carbs", f"{summary['total_carbs']:.1f}g")
                    with col4:
                        st.metric("Fat", f"{summary['total_fat']:.1f}g")
            else:
                st.info("No nutrition entries found for the selected date range")

def meals_page():
    """Meals management page"""
    st.markdown('<h1 class="main-header">🍽️ Meal Management</h1>', unsafe_allow_html=True)

    tab1, tab2, tab3 = st.tabs(["Create Meal", "Saved Meals", "Quick Log"])

    with tab1:
        st.subheader("Create New Meal")
        st.info("🥘 Create and save meal templates for easy logging")

        # Meal basic info
        st.subheader("Meal Information")
        meal_name = st.text_input("Meal Name*", placeholder="e.g., Burrito Bowl, Protein Smoothie")
        meal_description = st.text_area("Description", placeholder="Optional description of the meal")
        meal_type = st.selectbox("Meal Type", ["breakfast", "lunch", "dinner", "snack"])

        # Initialize meal ingredients in session state
        if 'meal_ingredients' not in st.session_state:
            st.session_state.meal_ingredients = []

        # Add ingredients section
        st.subheader("Add Ingredients from Food Database")
        search_food = st.text_input("🔍 Search for ingredients:", placeholder="Search foods to add to meal")

        if search_food:
            response = make_authenticated_request("GET", "/nutrition/foods", params={"q": search_food, "limit": 10})
            if response and response.status_code == 200:
                foods = response.json()
                if foods:
                    food_options = [f"{food['name']} ({food['brand'] or 'Generic'})" for food in foods]
                    selected_food = st.selectbox("Select ingredient:", food_options)

                    if selected_food:
                        food_index = food_options.index(selected_food)
                        selected_food_data = foods[food_index]

                        # Show nutrition info for this food
                        serving_size = selected_food_data.get('serving_size_g', 100)
                        serving_desc = selected_food_data.get('serving_description', f"{serving_size}g")
                        calories_per_serving = (selected_food_data['calories_per_100g'] * serving_size) / 100

                        st.info(f"📊 {selected_food_data['name']}: {calories_per_serving:.0f} cal per serving ({serving_desc})")

                        col1, col2, col3 = st.columns([2, 2, 1])
                        with col1:
                            servings = st.number_input("Number of servings", min_value=0.1, value=1.0, step=0.1, key="ingredient_servings")
                        with col2:
                            st.write(f"**Total calories:** {calories_per_serving * servings:.0f}")
                        with col3:
                            if st.button("➕ Add to Meal"):
                                # Calculate nutrition for this ingredient
                                total_calories = calories_per_serving * servings
                                total_protein = (selected_food_data['protein_per_100g'] * serving_size * servings) / 100
                                total_carbs = (selected_food_data['carbs_per_100g'] * serving_size * servings) / 100
                                total_fat = (selected_food_data['fat_per_100g'] * serving_size * servings) / 100

                                ingredient = {
                                    'food_id': selected_food_data['id'],
                                    'name': selected_food_data['name'],
                                    'servings': servings,
                                    'serving_description': serving_desc,
                                    'calories': total_calories,
                                    'protein': total_protein,
                                    'carbs': total_carbs,
                                    'fat': total_fat
                                }

                                st.session_state.meal_ingredients.append(ingredient)
                                st.success(f"Added {servings} serving(s) of {selected_food_data['name']} to meal!")
                                st.rerun()

        # Display current ingredients and nutrition totals
        if st.session_state.meal_ingredients:
            st.subheader("Current Meal Composition")

            total_calories = 0
            total_protein = 0
            total_carbs = 0
            total_fat = 0

            for i, ingredient in enumerate(st.session_state.meal_ingredients):
                col1, col2, col3 = st.columns([3, 2, 1])
                with col1:
                    st.write(f"• **{ingredient['name']}**")
                    st.write(f"  {ingredient['servings']} serving(s) ({ingredient['serving_description']} each)")
                with col2:
                    st.write(f"**{ingredient['calories']:.0f} cal**")
                    st.write(f"P: {ingredient['protein']:.1f}g | C: {ingredient['carbs']:.1f}g | F: {ingredient['fat']:.1f}g")
                with col3:
                    if st.button("🗑️", key=f"remove_ingredient_{i}"):
                        st.session_state.meal_ingredients.pop(i)
                        st.rerun()

                total_calories += ingredient['calories']
                total_protein += ingredient['protein']
                total_carbs += ingredient['carbs']
                total_fat += ingredient['fat']

            # Show meal totals
            st.subheader("🍽️ Meal Nutrition Totals")
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total Calories", f"{total_calories:.0f}")
            with col2:
                st.metric("Protein", f"{total_protein:.1f}g")
            with col3:
                st.metric("Carbs", f"{total_carbs:.1f}g")
            with col4:
                st.metric("Fat", f"{total_fat:.1f}g")

            # Save meal button
            if st.button("💾 Save Meal Template", type="primary"):
                if meal_name:
                    meal_data = {
                        "name": meal_name,
                        "description": meal_description,
                        "meal_type": meal_type,
                        "is_template": True,
                        "ingredients": st.session_state.meal_ingredients,
                        "total_calories": total_calories,
                        "total_protein": total_protein,
                        "total_carbs": total_carbs,
                        "total_fat": total_fat
                    }

                    response = make_authenticated_request("POST", "/meals/", data=meal_data)
                    if response and response.status_code == 200:
                        st.success("Meal template saved successfully!")
                        st.session_state.meal_ingredients = []
                        st.rerun()
                    else:
                        st.error("Failed to save meal template")
                else:
                    st.error("Please enter a meal name")
        else:
            st.info("Add ingredients from the food database to create your meal")

    with tab2:
        st.subheader("Saved Meals")

        # Get saved meals
        response = make_authenticated_request("GET", "/meals/", params={"templates_only": True})
        if response and response.status_code == 200:
            meals = response.json()
            if meals:
                # Meal management
                meal_options = [f"{meal['name']} ({meal['meal_type']})" for meal in meals]
                selected_meal = st.selectbox("Select meal to manage:", [""] + meal_options)

                if selected_meal:
                    meal_index = meal_options.index(selected_meal)
                    meal_data = meals[meal_index]

                    col1, col2, col3 = st.columns(3)
                    with col1:
                        if st.button("🍽️ Log This Meal", key=f"log_meal_{meal_data['id']}"):
                            st.session_state.logging_meal = meal_data['id']

                    with col2:
                        if st.button("✏️ Edit Meal", key=f"edit_meal_{meal_data['id']}"):
                            st.session_state.editing_meal = meal_data

                    with col3:
                        if st.button("🗑️ Delete Meal", key=f"delete_meal_{meal_data['id']}"):
                            delete_response = make_authenticated_request("DELETE", f"/meals/{meal_data['id']}")
                            if delete_response and delete_response.status_code == 200:
                                st.success("Meal deleted successfully!")
                                st.rerun()
                            else:
                                st.error("Failed to delete meal")

                    # Meal logging form
                    if 'logging_meal' in st.session_state and st.session_state.logging_meal == meal_data['id']:
                        st.subheader("🍽️ Log Meal")
                        with st.form(f"log_meal_form_{meal_data['id']}"):
                            col1, col2 = st.columns(2)
                            with col1:
                                portion = st.number_input("Portion multiplier", min_value=0.1, max_value=5.0, value=1.0, step=0.1)
                                meal_date = st.date_input("Date consumed", value=date.today())
                            with col2:
                                meal_time = st.time_input("Time consumed", value=datetime.now().time())

                            # Show what will be logged
                            if 'total_calories' in meal_data:
                                st.info(f"📊 This will log {meal_data['total_calories'] * portion:.0f} calories")

                            col1, col2 = st.columns(2)
                            with col1:
                                if st.form_submit_button("✅ Confirm Log"):
                                    # Combine date and time
                                    consumed_datetime = datetime.combine(meal_date, meal_time)

                                    log_data = {
                                        "portion_multiplier": portion,
                                        "consumed_at": consumed_datetime.isoformat()
                                    }

                                    log_response = make_authenticated_request("POST", f"/meals/{meal_data['id']}/log", data=log_data)
                                    if log_response and log_response.status_code == 200:
                                        st.success("Meal logged successfully!")
                                        del st.session_state.logging_meal
                                        st.rerun()
                                    else:
                                        st.error("Failed to log meal")
                            with col2:
                                if st.form_submit_button("❌ Cancel"):
                                    del st.session_state.logging_meal
                                    st.rerun()

                    # Show meal details
                    st.subheader(f"📋 {meal_data['name']}")
                    if meal_data.get('description'):
                        st.write(meal_data['description'])
                    st.write(f"**Type:** {meal_data['meal_type'].title()}")

                    # Show nutrition info if available
                    if 'total_calories' in meal_data:
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("Calories", f"{meal_data['total_calories']:.0f}")
                        with col2:
                            st.metric("Protein", f"{meal_data['total_protein']:.1f}g")
                        with col3:
                            st.metric("Carbs", f"{meal_data['total_carbs']:.1f}g")
                        with col4:
                            st.metric("Fat", f"{meal_data['total_fat']:.1f}g")

                        # Show ingredients if available
                        if 'ingredients' in meal_data and meal_data['ingredients']:
                            with st.expander("View Ingredients"):
                                for ingredient in meal_data['ingredients']:
                                    st.write(f"• {ingredient['servings']} serving(s) of {ingredient['name']}")
                                    st.write(f"  {ingredient['calories']:.0f} cal | P: {ingredient['protein']:.1f}g | C: {ingredient['carbs']:.1f}g | F: {ingredient['fat']:.1f}g")

                # Edit meal form
                if 'editing_meal' in st.session_state:
                    st.subheader("✏️ Edit Meal")
                    edit_meal = st.session_state.editing_meal

                    with st.form("edit_meal_form"):
                        edit_name = st.text_input("Meal Name", value=edit_meal['name'])
                        edit_description = st.text_area("Description", value=edit_meal.get('description', ''))
                        edit_type = st.selectbox("Meal Type", ["breakfast", "lunch", "dinner", "snack"],
                                               index=["breakfast", "lunch", "dinner", "snack"].index(edit_meal['meal_type']))

                        col1, col2 = st.columns(2)
                        with col1:
                            if st.form_submit_button("💾 Save Changes"):
                                update_data = {
                                    "name": edit_name,
                                    "description": edit_description,
                                    "meal_type": edit_type,
                                    "is_template": True
                                }

                                update_response = make_authenticated_request("PUT", f"/meals/{edit_meal['id']}", data=update_data)
                                if update_response and update_response.status_code == 200:
                                    st.success("Meal updated successfully!")
                                    del st.session_state.editing_meal
                                    st.rerun()
                                else:
                                    st.error("Failed to update meal")
                        with col2:
                            if st.form_submit_button("❌ Cancel"):
                                del st.session_state.editing_meal
                                st.rerun()

                # Display all meals
                st.subheader("📋 All Saved Meals")
                for meal in meals:
                    with st.expander(f"{meal['name']} ({meal['meal_type']})"):
                        if meal.get('description'):
                            st.write(meal['description'])
                        st.write(f"Created: {pd.to_datetime(meal['created_at'], format='ISO8601').strftime('%Y-%m-%d')}")
            else:
                st.info("No saved meals found. Create your first meal template!")

    with tab3:
        st.subheader("Quick Log Meal")
        st.info("🚀 Quickly log a saved meal")

        # Get saved meals for quick logging
        response = make_authenticated_request("GET", "/meals/", params={"templates_only": True})
        if response and response.status_code == 200:
            meals = response.json()
            if meals:
                meal_options = [f"{meal['name']} ({meal['meal_type']})" for meal in meals]
                selected_meal = st.selectbox("Select meal to log:", meal_options)

                if selected_meal:
                    meal_index = meal_options.index(selected_meal)
                    meal_data = meals[meal_index]

                    col1, col2 = st.columns(2)
                    with col1:
                        portion_multiplier = st.slider("Portion size", min_value=0.1, max_value=3.0, value=1.0, step=0.1)
                        quick_date = st.date_input("Date consumed", value=date.today(), key="quick_meal_date")
                    with col2:
                        quick_time = st.time_input("Time consumed", value=datetime.now().time(), key="quick_meal_time")

                    # Show nutrition preview
                    if 'total_calories' in meal_data:
                        st.write(f"**Nutrition Preview:** {meal_data['total_calories'] * portion_multiplier:.0f} calories")
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.write(f"Protein: {meal_data['total_protein'] * portion_multiplier:.1f}g")
                        with col2:
                            st.write(f"Carbs: {meal_data['total_carbs'] * portion_multiplier:.1f}g")
                        with col3:
                            st.write(f"Fat: {meal_data['total_fat'] * portion_multiplier:.1f}g")

                    if st.button("🍽️ Log Meal Now"):
                        # Combine date and time
                        consumed_datetime = datetime.combine(quick_date, quick_time)

                        log_data = {
                            "portion_multiplier": portion_multiplier,
                            "consumed_at": consumed_datetime.isoformat()
                        }

                        log_response = make_authenticated_request("POST", f"/meals/{meal_data['id']}/log", data=log_data)
                        if log_response and log_response.status_code == 200:
                            result = log_response.json()
                            st.success(f"✅ {result.get('message', 'Meal logged successfully!')}")
                            if 'entries_created' in result:
                                st.info(f"Created {result['entries_created']} nutrition entries")
                        else:
                            st.error("Failed to log meal")
            else:
                st.info("No saved meals available. Create some meal templates first!")

def exercise_page():
    """Exercise tracking page"""
    st.markdown('<h1 class="main-header">🏋️ Exercise Tracking</h1>', unsafe_allow_html=True)

    tab1, tab2, tab3 = st.tabs(["Log Workout", "Exercise History", "Fitness Data"])

    with tab1:
        st.subheader("Log Workout")
        st.info("💪 Track your workouts and activities")

        with st.form("exercise_form"):
            # Exercise type selection
            response = make_authenticated_request("GET", "/exercise/types")
            if response and response.status_code == 200:
                exercise_types = response.json()
                if exercise_types:
                    type_options = [f"{ex['name']} ({ex['category']})" for ex in exercise_types]
                    selected_type = st.selectbox("Exercise Type", type_options)

                    if selected_type:
                        type_index = type_options.index(selected_type)
                        exercise_type_data = exercise_types[type_index]

                        col1, col2, col3 = st.columns(3)
                        with col1:
                            duration = st.number_input("Duration (minutes)", min_value=1.0, value=30.0, step=1.0, key="exercise_duration")
                            intensity = st.selectbox("Intensity", ["low", "moderate", "high", "very_high"], key="exercise_intensity")
                            calories = st.number_input("Calories Burned (optional)", min_value=0.0, step=1.0, key="exercise_calories")
                        with col2:
                            steps = st.number_input("Steps (optional)", min_value=0, step=1, key="exercise_steps")
                            distance = st.number_input("Distance (miles)", min_value=0.0, step=0.1, key="exercise_distance")
                            heart_rate = st.number_input("Avg Heart Rate (optional)", min_value=0, max_value=220, step=1, key="exercise_heart_rate")
                        with col3:
                            exercise_date = st.date_input("Date performed", value=date.today(), key="exercise_date")
                            exercise_time = st.time_input("Time performed", value=datetime.now().time(), key="exercise_time")

                        # Strength training specific
                        if exercise_type_data['category'] == 'strength':
                            st.subheader("Strength Training Details")
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                sets = st.number_input("Sets", min_value=1, value=3, step=1, key="strength_sets")
                            with col2:
                                reps = st.number_input("Reps", min_value=1, value=10, step=1, key="strength_reps")
                            with col3:
                                weight = st.number_input("Weight (lbs)", min_value=0.0, step=2.5, key="strength_weight")
                        else:
                            sets = reps = weight = None

                        notes = st.text_area("Notes (optional)", key="exercise_notes")

                        if st.form_submit_button("💾 Log Workout"):
                            # Combine date and time
                            performed_datetime = datetime.combine(exercise_date, exercise_time)

                            exercise_data = {
                                "exercise_type_id": exercise_type_data['id'],
                                "duration_minutes": duration,
                                "intensity": intensity,
                                "calories_burned": calories if calories > 0 else None,
                                "steps": steps if steps > 0 else None,
                                "distance_miles": distance if distance > 0 else None,
                                "heart_rate_avg": heart_rate if heart_rate > 0 else None,
                                "sets": sets,
                                "reps": reps,
                                "weight_lbs": weight if weight and weight > 0 else None,
                                "notes": notes,
                                "performed_at": performed_datetime.isoformat()
                            }

                            response = make_authenticated_request("POST", "/exercise/entries", data=exercise_data)
                            if response and response.status_code == 200:
                                st.success("Workout logged successfully!")
                                st.rerun()
                            else:
                                st.error("Failed to log workout")
                else:
                    st.warning("No exercise types found. Please add some exercise types first.")

        # Quick fitness tracker sync (outside the main form)
        st.subheader("📱 Sync Fitness Tracker Data")
        with st.expander("Sync Daily Activity"):
            with st.form("sync_fitness_form"):
                sync_date = st.date_input("Date", value=date.today())
                col1, col2 = st.columns(2)
                with col1:
                    sync_steps = st.number_input("Steps", min_value=0, step=100, key="sync_steps")
                    sync_calories = st.number_input("Calories Burned", min_value=0.0, step=10.0, key="sync_calories")
                with col2:
                    sync_distance = st.number_input("Distance (miles)", min_value=0.0, step=0.1, key="sync_distance")
                    sync_heart_rate = st.number_input("Avg Heart Rate", min_value=0, max_value=220, step=1, key="sync_heart_rate")

                if st.form_submit_button("🔄 Sync Data"):
                    sync_data = {
                        "steps": sync_steps if sync_steps > 0 else None,
                        "calories_burned": sync_calories if sync_calories > 0 else None,
                        "distance_miles": sync_distance if sync_distance > 0 else None,
                        "heart_rate_avg": sync_heart_rate if sync_heart_rate > 0 else None,
                        "sync_date": sync_date.isoformat()
                    }

                    response = make_authenticated_request("POST", "/exercise/sync-fitness-data", data=sync_data)
                    if response and response.status_code == 200:
                        st.success("Fitness data synced successfully!")
                        st.rerun()
                    else:
                        st.error("Failed to sync fitness data")

    with tab2:
        st.subheader("Exercise History")

        # Date filter
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("From date", value=date.today() - timedelta(days=30))
        with col2:
            end_date = st.date_input("To date", value=date.today())

        # Get exercise entries
        params = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "limit": 100
        }
        response = make_authenticated_request("GET", "/exercise/entries", params=params)

        if response and response.status_code == 200:
            entries = response.json()
            if entries:
                df = pd.DataFrame(entries)
                df['performed_at'] = pd.to_datetime(df['performed_at'], format='ISO8601').dt.strftime('%Y-%m-%d %H:%M')

                # Entry management
                st.subheader("📊 Entry Management")
                entry_options = [f"{row['performed_at']} - {row.get('duration_minutes', 0):.0f} min" for _, row in df.iterrows()]
                selected_entry = st.selectbox("Select entry to edit/delete:", [""] + entry_options)

                if selected_entry:
                    entry_index = entry_options.index(selected_entry)
                    entry_data = entries[entry_index]

                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("✏️ Edit Entry", key=f"edit_exercise_{entry_data['id']}"):
                            st.session_state.editing_exercise = entry_data
                    with col2:
                        if st.button("🗑️ Delete Entry", key=f"delete_exercise_{entry_data['id']}"):
                            delete_response = make_authenticated_request("DELETE", f"/exercise/entries/{entry_data['id']}")
                            if delete_response and delete_response.status_code == 200:
                                st.success("Entry deleted successfully!")
                                st.rerun()
                            else:
                                st.error("Failed to delete entry")

                # Edit form (simplified for space)
                if 'editing_exercise' in st.session_state:
                    st.subheader("✏️ Edit Exercise Entry")
                    edit_entry = st.session_state.editing_exercise

                    with st.form("edit_exercise_form"):
                        edit_duration = st.number_input("Duration (min)", value=float(edit_entry.get('duration_minutes', 0)), key="edit_duration")
                        edit_calories = st.number_input("Calories", value=float(edit_entry.get('calories_burned', 0)), key="edit_calories")
                        edit_notes = st.text_area("Notes", value=edit_entry.get('notes', ''), key="edit_notes")

                        col1, col2 = st.columns(2)
                        with col1:
                            if st.form_submit_button("💾 Save"):
                                update_data = {
                                    "duration_minutes": edit_duration,
                                    "calories_burned": edit_calories,
                                    "notes": edit_notes
                                }
                                update_response = make_authenticated_request("PUT", f"/exercise/entries/{edit_entry['id']}", data=update_data)
                                if update_response and update_response.status_code == 200:
                                    st.success("Entry updated!")
                                    del st.session_state.editing_exercise
                                    st.rerun()
                        with col2:
                            if st.form_submit_button("❌ Cancel"):
                                del st.session_state.editing_exercise
                                st.rerun()

                # Display entries
                st.subheader("📋 All Entries")
                display_cols = ['performed_at', 'duration_minutes', 'calories_burned', 'steps', 'distance_miles']
                available_cols = [col for col in display_cols if col in df.columns]
                st.dataframe(df[available_cols], use_container_width=True)
            else:
                st.info("No exercise entries found")

    with tab3:
        st.subheader("Fitness Summary")

        # Get exercise summary
        response = make_authenticated_request("GET", "/exercise/daily-summary")
        if response and response.status_code == 200:
            summary = response.json()

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Calories Burned", f"{summary['total_calories_burned']:.0f}")
            with col2:
                st.metric("Total Steps", f"{summary['total_steps']:,}")
            with col3:
                st.metric("Distance", f"{summary['total_distance_miles']:.1f} mi")
            with col4:
                st.metric("Duration", f"{summary['total_duration_minutes']:.0f} min")

def analytics_page():
    """Analytics and insights page"""
    st.markdown('<h1 class="main-header">📊 Analytics & Insights</h1>', unsafe_allow_html=True)

    tab1, tab2, tab3 = st.tabs(["Progress Overview", "Detailed Charts", "Macro Analysis"])

    with tab1:
        st.subheader("Progress Overview")

        # Get progress summary
        response = make_authenticated_request("GET", "/analytics/progress-summary")
        if response and response.status_code == 200:
            progress = response.json()

            # Key metrics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                current_weight = progress.get('current_weight_lbs')
                if current_weight:
                    st.metric("Current Weight", f"{current_weight:.1f} lbs")
                else:
                    st.metric("Current Weight", "No data")

            with col2:
                weight_change = progress.get('weight_change_lbs')
                if weight_change is not None:
                    st.metric("Weight Change", f"{weight_change:+.1f} lbs", delta=f"{weight_change:+.1f}")
                else:
                    st.metric("Weight Change", "No data")

            with col3:
                goal_weight = progress.get('goal_weight_lbs')
                if goal_weight and current_weight:
                    remaining = goal_weight - current_weight
                    st.metric("To Goal", f"{remaining:+.1f} lbs")
                else:
                    st.metric("To Goal", "Not set")

            with col4:
                tracking_days = progress.get('tracking_days', 0)
                st.metric("Tracking Days", f"{tracking_days}")

            # Weight trend chart
            st.subheader("📈 Weight Trend (60 days)")
            response = make_authenticated_request("GET", "/analytics/weight-trend", params={"days": 60})
            if response and response.status_code == 200:
                weight_data = response.json()
                if weight_data:
                    df = pd.DataFrame(weight_data)
                    df['date'] = pd.to_datetime(df['date'], format='ISO8601')

                    fig = px.line(df, x='date', y='weight_lbs', title='Weight Progress')
                    fig.update_layout(xaxis_title="Date", yaxis_title="Weight (lbs)")

                    # Add goal line if available
                    if goal_weight:
                        fig.add_hline(y=goal_weight, line_dash="dash", line_color="red",
                                     annotation_text=f"Goal: {goal_weight:.1f} lbs")

                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.info("No weight data available for chart")

    with tab2:
        st.subheader("Detailed Analysis")

        # Biometric trends with overlay
        st.subheader("📊 Biometric Trends")

        # Metric selection
        available_metrics = ['weight_lbs', 'body_fat_percentage', 'muscle_mass_lbs', 'water_percentage', 'bmi']
        selected_metrics = st.multiselect(
            "Select up to 2 metrics to overlay:",
            available_metrics,
            default=['weight_lbs', 'body_fat_percentage'],
            max_selections=2
        )

        days_range = st.slider("Days to include", min_value=7, max_value=365, value=90, step=7)

        if selected_metrics:
            response = make_authenticated_request("GET", "/analytics/biometric-trends",
                                                params={"metrics": selected_metrics, "days": days_range})
            if response and response.status_code == 200:
                trends_data = response.json()

                if any(trends_data.values()):
                    fig = make_subplots(specs=[[{"secondary_y": True}]])

                    colors = ['blue', 'red']
                    for i, metric in enumerate(selected_metrics):
                        if metric in trends_data and trends_data[metric]:
                            df_metric = pd.DataFrame(trends_data[metric])
                            df_metric['date'] = pd.to_datetime(df_metric['date'], format='ISO8601')

                            fig.add_trace(
                                go.Scatter(
                                    x=df_metric['date'],
                                    y=df_metric['value'],
                                    name=metric.replace('_', ' ').title(),
                                    line=dict(color=colors[i])
                                ),
                                secondary_y=(i > 0)
                            )

                    # Update layout
                    fig.update_layout(title="Biometric Trends Overlay")
                    fig.update_xaxes(title_text="Date")

                    if len(selected_metrics) > 0:
                        fig.update_yaxes(title_text=selected_metrics[0].replace('_', ' ').title(), secondary_y=False)
                    if len(selected_metrics) > 1:
                        fig.update_yaxes(title_text=selected_metrics[1].replace('_', ' ').title(), secondary_y=True)

                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.info("No data available for selected metrics")

        # Nutrition trends
        st.subheader("🍎 Nutrition Trends")
        response = make_authenticated_request("GET", "/analytics/nutrition-trends", params={"days": 30})
        if response and response.status_code == 200:
            nutrition_data = response.json()
            if nutrition_data:
                df_nutrition = pd.DataFrame(nutrition_data)
                df_nutrition['date'] = pd.to_datetime(df_nutrition['date'], format='ISO8601')

                # Create nutrition chart
                fig = go.Figure()
                fig.add_trace(go.Scatter(x=df_nutrition['date'], y=df_nutrition['calories'],
                                       name='Calories', line=dict(color='orange')))
                fig.add_trace(go.Scatter(x=df_nutrition['date'], y=df_nutrition['protein']*4,
                                       name='Protein (cal)', line=dict(color='red')))
                fig.add_trace(go.Scatter(x=df_nutrition['date'], y=df_nutrition['carbs']*4,
                                       name='Carbs (cal)', line=dict(color='blue')))
                fig.add_trace(go.Scatter(x=df_nutrition['date'], y=df_nutrition['fat']*9,
                                       name='Fat (cal)', line=dict(color='green')))

                fig.update_layout(title="Daily Nutrition Trends", xaxis_title="Date", yaxis_title="Calories")
                st.plotly_chart(fig, use_container_width=True)

    with tab3:
        st.subheader("Macro Analysis")

        # Today's macro breakdown
        st.subheader("🥧 Today's Macro Breakdown")
        response = make_authenticated_request("GET", "/analytics/macro-breakdown")
        if response and response.status_code == 200:
            macro_data = response.json()
            if macro_data["total_calories"] > 0:
                # Pie chart
                labels = ['Protein', 'Carbs', 'Fat']
                values = [
                    macro_data["protein_percentage"],
                    macro_data["carbs_percentage"],
                    macro_data["fat_percentage"]
                ]
                colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']

                fig = px.pie(values=values, names=labels, title='Macro Distribution',
                           color_discrete_sequence=colors)
                st.plotly_chart(fig, use_container_width=True)

                # Macro details
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("Total Calories", f"{macro_data['total_calories']:.0f}")
                with col2:
                    st.metric("Protein", f"{macro_data['protein_grams']:.1f}g")
                with col3:
                    st.metric("Carbs", f"{macro_data['carbs_grams']:.1f}g")
                with col4:
                    st.metric("Fat", f"{macro_data['fat_grams']:.1f}g")
            else:
                st.info("No nutrition data for today")

        # Calorie balance
        st.subheader("⚖️ Calorie Balance (7 days)")
        response = make_authenticated_request("GET", "/analytics/calorie-balance", params={"days": 7})
        if response and response.status_code == 200:
            balance_data = response.json()
            if balance_data:
                df_balance = pd.DataFrame(balance_data)
                df_balance['date'] = pd.to_datetime(df_balance['date'], format='ISO8601')

                fig = go.Figure()
                fig.add_trace(go.Bar(x=df_balance['date'], y=df_balance['calories_consumed'],
                                   name='Calories In', marker_color='lightblue'))
                fig.add_trace(go.Bar(x=df_balance['date'], y=df_balance['total_expenditure'],
                                   name='Calories Out', marker_color='lightcoral'))
                fig.add_trace(go.Scatter(x=df_balance['date'], y=df_balance['net_calories'],
                                       name='Net Calories', line=dict(color='black', width=3)))

                fig.update_layout(title="Daily Calorie Balance", xaxis_title="Date", yaxis_title="Calories")
                st.plotly_chart(fig, use_container_width=True)

def main():
    """Main application"""
    if not st.session_state.authenticated:
        login_page()
    else:
        # Sidebar navigation
        st.sidebar.title("Navigation")
        page = st.sidebar.selectbox("Choose a page", [
            "Dashboard", "Biometrics", "Nutrition", "Meals", "Exercise", "Analytics"
        ])
        
        if st.sidebar.button("Logout"):
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.session_state.user_data = None
            st.rerun()
        
        # Page routing
        if page == "Dashboard":
            dashboard_page()
        elif page == "Biometrics":
            biometrics_page()
        elif page == "Nutrition":
            nutrition_page()
        elif page == "Meals":
            meals_page()
        elif page == "Exercise":
            exercise_page()
        elif page == "Analytics":
            analytics_page()
        else:
            st.info(f"{page} page coming soon!")

if __name__ == "__main__":
    main()
