#!/usr/bin/env python3
"""
Test the complete biometric entry flow with custom date/time
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from app.main import app
from app.database import get_db, engine
from app.models import Base, User, BiometricEntry
from sqlalchemy.orm import sessionmaker

# Create test client
client = TestClient(app)

def test_biometric_datetime_integration():
    """Test the complete biometric entry flow with custom timestamps"""
    
    print("🧪 Testing Biometric Date/Time Integration...")
    
    # Create test database
    Base.metadata.create_all(bind=engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    try:
        # Test user data
        test_user_data = {
            "username": "datetime_test_user",
            "email": "<EMAIL>",
            "password": "testpass123",
            "height_inches": 70,
            "date_of_birth": "1990-01-01",
            "gender": "male",
            "activity_level": "moderately_active"
        }
        
        # Step 1: Register user
        print("\n1️⃣ Registering test user...")
        register_response = client.post("/auth/register", json=test_user_data)
        if register_response.status_code == 200:
            print("✅ User registered successfully")
        elif register_response.status_code == 400 and "already registered" in register_response.text:
            print("ℹ️ User already exists, continuing...")
        else:
            print(f"❌ Registration failed: {register_response.status_code}")
            return False
        
        # Step 2: Login
        print("\n2️⃣ Logging in...")
        login_data = {
            "username": test_user_data["username"],
            "password": test_user_data["password"]
        }
        login_response = client.post("/auth/token", data=login_data)
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        token_data = login_response.json()
        access_token = token_data["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        print("✅ Login successful")
        
        # Step 3: Create biometric entry with custom date/time
        print("\n3️⃣ Creating biometric entry with custom date/time...")
        
        # Custom timestamp - 3 days ago at 8:30 AM
        custom_datetime = datetime.now() - timedelta(days=3)
        custom_datetime = custom_datetime.replace(hour=8, minute=30, second=0, microsecond=0)
        
        entry_data = {
            "weight_lbs": 178.5,
            "body_fat_percentage": 16.2,
            "muscle_mass_lbs": 77.8,
            "water_percentage": 61.5,
            "recorded_at": custom_datetime.isoformat()
        }
        
        print(f"   Sending entry with timestamp: {custom_datetime}")
        
        create_response = client.post("/biometrics/", json=entry_data, headers=headers)
        if create_response.status_code != 200:
            print(f"❌ Failed to create entry: {create_response.status_code} - {create_response.text}")
            return False
        
        created_entry = create_response.json()
        print(f"   ✅ Entry created with ID: {created_entry['id']}")
        print(f"   📅 Stored timestamp: {created_entry['recorded_at']}")
        
        # Verify the timestamp matches
        stored_datetime = datetime.fromisoformat(created_entry['recorded_at'].replace('Z', '+00:00'))
        if stored_datetime.replace(tzinfo=None) == custom_datetime:
            print("   ✅ Custom timestamp correctly stored!")
        else:
            print(f"   ❌ Timestamp mismatch!")
            print(f"      Expected: {custom_datetime}")
            print(f"      Got: {stored_datetime.replace(tzinfo=None)}")
            return False
        
        # Step 4: Create another entry with different timestamp
        print("\n4️⃣ Creating second entry with different timestamp...")
        
        # Different timestamp - 1 day ago at 6:15 PM
        second_datetime = datetime.now() - timedelta(days=1)
        second_datetime = second_datetime.replace(hour=18, minute=15, second=0, microsecond=0)
        
        second_entry_data = {
            "weight_lbs": 177.8,
            "body_fat_percentage": 15.9,
            "muscle_mass_lbs": 78.1,
            "recorded_at": second_datetime.isoformat()
        }
        
        second_response = client.post("/biometrics/", json=second_entry_data, headers=headers)
        if second_response.status_code != 200:
            print(f"❌ Failed to create second entry: {second_response.status_code}")
            return False
        
        second_entry = second_response.json()
        print(f"   ✅ Second entry created with timestamp: {second_entry['recorded_at']}")
        
        # Step 5: Get all entries and verify timestamps
        print("\n5️⃣ Retrieving all entries to verify timestamps...")
        
        get_response = client.get("/biometrics/", headers=headers)
        if get_response.status_code != 200:
            print(f"❌ Failed to get entries: {get_response.status_code}")
            return False
        
        entries = get_response.json()
        print(f"   Found {len(entries)} entries:")
        
        for i, entry in enumerate(entries):
            entry_datetime = datetime.fromisoformat(entry['recorded_at'].replace('Z', '+00:00'))
            print(f"     Entry {i+1}: {entry_datetime.replace(tzinfo=None)} - Weight: {entry['weight_lbs']} lbs")
        
        # Verify entries are in correct chronological order (newest first)
        timestamps = [datetime.fromisoformat(e['recorded_at'].replace('Z', '+00:00')) for e in entries]
        if timestamps == sorted(timestamps, reverse=True):
            print("   ✅ Entries are correctly ordered by timestamp (newest first)")
        else:
            print("   ⚠️ Entries may not be in correct chronological order")
        
        # Step 6: Update an entry's timestamp
        print("\n6️⃣ Testing timestamp update...")
        
        entry_to_update = entries[0]  # Get the most recent entry
        original_timestamp = entry_to_update['recorded_at']
        
        # New timestamp - 5 days ago at 2:45 PM
        new_datetime = datetime.now() - timedelta(days=5)
        new_datetime = new_datetime.replace(hour=14, minute=45, second=0, microsecond=0)
        
        update_data = {
            "weight_lbs": 179.2,
            "recorded_at": new_datetime.isoformat()
        }
        
        update_response = client.put(f"/biometrics/{entry_to_update['id']}", json=update_data, headers=headers)
        if update_response.status_code != 200:
            print(f"❌ Failed to update entry: {update_response.status_code}")
            return False
        
        updated_entry = update_response.json()
        print(f"   ✅ Entry updated:")
        print(f"      Original timestamp: {original_timestamp}")
        print(f"      New timestamp: {updated_entry['recorded_at']}")
        print(f"      Weight updated: {updated_entry['weight_lbs']} lbs")
        
        # Step 7: Verify BMR calculations work with custom timestamps
        print("\n7️⃣ Verifying BMR calculations with custom timestamps...")
        
        if updated_entry.get('bmr_calculated'):
            print(f"   ✅ BMR calculated: {updated_entry['bmr_calculated']:.1f} cal/day")
        else:
            print("   ⚠️ No BMR calculation found")
        
        if updated_entry.get('bmr_predicted'):
            print(f"   ✅ BMR predicted: {updated_entry['bmr_predicted']:.1f} cal/day")
        else:
            print("   ℹ️ No ML BMR prediction (may need more data)")
        
        print("\n🎉 Biometric Date/Time Integration Test Completed Successfully!")
        print("\n📋 Summary:")
        print("   ✅ Custom recorded_at timestamps are properly handled")
        print("   ✅ Multiple entries with different timestamps work correctly")
        print("   ✅ Entries are stored and retrieved in correct order")
        print("   ✅ Timestamp updates work properly")
        print("   ✅ BMR calculations work with custom timestamps")
        print("   ✅ API endpoints handle datetime fields correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Biometric Date/Time Integration Test")
    print("=" * 60)
    success = test_biometric_datetime_integration()
    sys.exit(0 if success else 1)
