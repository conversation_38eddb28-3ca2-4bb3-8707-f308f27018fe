#!/usr/bin/env python3
"""
Test BMR ML integration with the FastAPI endpoints
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime, timedelta

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER = {
    "username": "bmr_test_user",
    "email": "<EMAIL>",
    "password": "testpass123",
    "height_inches": 70,
    "date_of_birth": "1990-01-01",
    "gender": "male",
    "activity_level": "moderately_active"
}

def test_bmr_integration():
    """Test the complete BMR ML integration"""
    
    print("🧪 Testing BMR ML Integration...")
    
    # Step 1: Register test user
    print("\n1️⃣ Registering test user...")
    try:
        response = requests.post(f"{BASE_URL}/auth/register", json=TEST_USER)
        if response.status_code == 200:
            print("✅ User registered successfully")
        elif response.status_code == 400 and "already registered" in response.text:
            print("ℹ️ User already exists, continuing...")
        else:
            print(f"❌ Registration failed: {response.status_code} - {response.text}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Please start the FastAPI server first.")
        print("   Run: python -m uvicorn app.main:app --reload --port 8000")
        return False
    
    # Step 2: Login
    print("\n2️⃣ Logging in...")
    login_data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    response = requests.post(f"{BASE_URL}/auth/token", data=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return False
    
    token_data = response.json()
    access_token = token_data["access_token"]
    headers = {"Authorization": f"Bearer {access_token}"}
    print("✅ Login successful")
    
    # Step 3: Create multiple biometric entries
    print("\n3️⃣ Creating biometric entries...")
    base_weight = 180.0
    entries_created = 0
    
    for i in range(12):  # Create 12 entries over 12 days
        entry_date = datetime.now() - timedelta(days=11-i)
        weight = base_weight - (i * 0.4)  # Gradual weight loss
        body_fat = 16.0 - (i * 0.08)
        
        entry_data = {
            "weight_lbs": weight,
            "body_fat_percentage": body_fat,
            "muscle_mass_lbs": 75.0 + (i * 0.1),
            "water_percentage": 60.0 + (i % 3 * 0.3),
            "recorded_at": entry_date.isoformat()
        }
        
        response = requests.post(f"{BASE_URL}/biometrics/", json=entry_data, headers=headers)
        if response.status_code == 200:
            entries_created += 1
            entry_result = response.json()
            print(f"   Entry {i+1}: Weight {weight:.1f} lbs, BMR calculated: {entry_result.get('bmr_calculated', 'N/A'):.0f}, BMR predicted: {entry_result.get('bmr_predicted', 'N/A')}")
        else:
            print(f"   ❌ Failed to create entry {i+1}: {response.status_code}")
    
    print(f"✅ Created {entries_created} biometric entries")
    
    # Step 4: Train BMR model
    print("\n4️⃣ Training BMR model...")
    response = requests.post(f"{BASE_URL}/biometrics/train-bmr-model", headers=headers)
    if response.status_code == 200:
        training_result = response.json()
        print(f"✅ Model training result:")
        print(f"   Success: {training_result.get('success')}")
        print(f"   Training samples: {training_result.get('training_samples', 'N/A')}")
        print(f"   Test samples: {training_result.get('test_samples', 'N/A')}")
        print(f"   MAE: {training_result.get('mae', 'N/A'):.2f}")
        print(f"   R² Score: {training_result.get('r2_score', 'N/A'):.3f}")
        
        if training_result.get('feature_importance'):
            print("   Top features:")
            importance = training_result['feature_importance']
            sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:5]
            for feature, imp in sorted_features:
                print(f"     {feature}: {imp:.3f}")
    else:
        print(f"❌ Model training failed: {response.status_code} - {response.text}")
        return False
    
    # Step 5: Get BMR insights
    print("\n5️⃣ Getting BMR insights...")
    response = requests.get(f"{BASE_URL}/biometrics/bmr-insights", headers=headers)
    if response.status_code == 200:
        insights = response.json()
        print(f"✅ BMR insights:")
        print(f"   Current BMR: {insights.get('current_bmr', 'N/A'):.0f} cal/day")
        print(f"   BMR change: {insights.get('bmr_change', 'N/A'):+.0f} cal")
        print(f"   BMR trend: {insights.get('bmr_trend', 'N/A')}")
        print(f"   Weight change: {insights.get('weight_change_lbs', 'N/A'):+.1f} lbs")
        print(f"   Analysis period: {insights.get('analysis_period_days', 'N/A')} days")
        
        if insights.get('recommendations'):
            print("   Recommendations:")
            for rec in insights['recommendations']:
                print(f"     • {rec}")
    else:
        print(f"❌ Failed to get BMR insights: {response.status_code} - {response.text}")
        return False
    
    # Step 6: Verify biometric entries have ML predictions
    print("\n6️⃣ Verifying ML predictions in entries...")
    response = requests.get(f"{BASE_URL}/biometrics/", headers=headers, params={"limit": 5})
    if response.status_code == 200:
        entries = response.json()
        predicted_count = sum(1 for entry in entries if entry.get('bmr_predicted'))
        print(f"✅ Found {predicted_count}/{len(entries)} entries with ML predictions")
        
        if predicted_count > 0:
            print("   Recent entries with predictions:")
            for entry in entries[:3]:
                if entry.get('bmr_predicted'):
                    calc_bmr = entry.get('bmr_calculated', 0)
                    pred_bmr = entry.get('bmr_predicted', 0)
                    diff = pred_bmr - calc_bmr
                    print(f"     Weight: {entry.get('weight_lbs', 'N/A'):.1f} lbs | Calculated: {calc_bmr:.0f} | Predicted: {pred_bmr:.0f} | Diff: {diff:+.0f}")
    else:
        print(f"❌ Failed to get biometric entries: {response.status_code}")
        return False
    
    print("\n🎉 BMR ML Integration Test Completed Successfully!")
    print("\n📋 Summary:")
    print("   ✅ BMR prediction engine implemented")
    print("   ✅ ML model training working")
    print("   ✅ BMR insights generation working")
    print("   ✅ API endpoints functional")
    print("   ✅ Database integration complete")
    
    return True

if __name__ == "__main__":
    print("BMR ML Integration Test")
    print("=" * 50)
    success = test_bmr_integration()
    sys.exit(0 if success else 1)
