#!/usr/bin/env python3
"""
Test script for BMR ML prediction engine
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database import Base
from app.models import User, BiometricEntry
from app.bmr_prediction import bmr_predictor

def test_bmr_ml():
    """Test the BMR ML prediction functionality"""
    
    # Create in-memory SQLite database for testing
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(bind=engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Create a test user
        test_user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="dummy",
            height_inches=69,  # 5'9"
            date_of_birth=datetime.now().date() - timedelta(days=30*365),  # 30 years old
            gender="male",
            activity_level="moderately_active"
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        # Create some sample biometric entries
        base_weight = 180.0
        for i in range(15):  # Create 15 entries over 15 days
            entry_date = datetime.now() - timedelta(days=14-i)
            weight = base_weight - (i * 0.3)  # Gradual weight loss
            body_fat = 15.0 - (i * 0.1)
            
            entry = BiometricEntry(
                user_id=test_user.id,
                recorded_at=entry_date,
                weight_lbs=weight,
                body_fat_percentage=body_fat,
                muscle_mass_lbs=75.0 + (i * 0.1),
                water_percentage=60.0 + (i % 3 * 0.5)
            )
            db.add(entry)
        
        db.commit()
        
        # Test standard BMR calculation
        latest_entry = db.query(BiometricEntry).filter(
            BiometricEntry.user_id == test_user.id
        ).order_by(BiometricEntry.recorded_at.desc()).first()
        
        standard_bmr = bmr_predictor.calculate_standard_bmr(
            test_user, latest_entry.weight_lbs, latest_entry.body_fat_percentage
        )
        print(f"Standard BMR calculation: {standard_bmr:.1f} calories/day")
        
        # Test feature extraction
        features = bmr_predictor.extract_features(test_user, latest_entry, db)
        print(f"Extracted features: {len(features) if features else 0} features")
        if features:
            feature_names = bmr_predictor.feature_names
            for name, value in zip(feature_names, features):
                print(f"  {name}: {value:.2f}")
        
        # Test training data preparation
        X, y = bmr_predictor.prepare_training_data(db)
        print(f"Training data: {len(X)} samples, {len(y)} targets")
        
        if len(X) >= 10:
            # Test model training
            training_result = bmr_predictor.train(db)
            print(f"Training result: {training_result}")
            
            if training_result["success"]:
                # Test prediction
                predicted_bmr = bmr_predictor.predict_bmr(test_user, latest_entry, db)
                print(f"ML-predicted BMR: {predicted_bmr:.1f} calories/day")
                
                # Test insights
                insights = bmr_predictor.get_bmr_insights(test_user, db)
                print(f"BMR insights: {insights}")
                
                print("\n✅ BMR ML prediction engine is working correctly!")
                return True
            else:
                print(f"❌ Model training failed: {training_result['message']}")
                return False
        else:
            print(f"❌ Insufficient training data: {len(X)} samples (need at least 10)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing BMR ML: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("Testing BMR ML Prediction Engine...")
    success = test_bmr_ml()
    sys.exit(0 if success else 1)
