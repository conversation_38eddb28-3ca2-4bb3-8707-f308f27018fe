#!/usr/bin/env python3
"""
Test BMR ML with existing database data
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database import get_db
from app.models import User, BiometricEntry
from app.bmr_prediction import bmr_predictor

def test_bmr_with_existing_data():
    """Test BMR ML with existing database data"""
    
    print("🧪 Testing BMR ML with Existing Data...")
    
    # Connect to existing database
    engine = create_engine("sqlite:///./weightloss_tracker.db")
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Get existing users
        users = db.query(User).all()
        print(f"📊 Found {len(users)} users in database")
        
        if not users:
            print("❌ No users found in database")
            return False
        
        # Get biometric entries
        total_entries = db.query(BiometricEntry).count()
        print(f"📊 Found {total_entries} biometric entries")
        
        if total_entries < 10:
            print("⚠️ Insufficient data for ML training (need at least 10 entries)")
            return False
        
        # Test with first user
        test_user = users[0]
        print(f"👤 Testing with user: {test_user.username}")
        
        # Get user's biometric entries
        user_entries = db.query(BiometricEntry).filter(
            BiometricEntry.user_id == test_user.id,
            BiometricEntry.weight_lbs.isnot(None)
        ).order_by(BiometricEntry.recorded_at.desc()).limit(5).all()
        
        print(f"📊 User has {len(user_entries)} biometric entries")
        
        if not user_entries:
            print("❌ No biometric entries for test user")
            return False
        
        latest_entry = user_entries[0]
        
        # Test standard BMR calculation
        standard_bmr = bmr_predictor.calculate_standard_bmr(
            test_user, latest_entry.weight_lbs, latest_entry.body_fat_percentage
        )
        print(f"🧮 Standard BMR: {standard_bmr:.1f} cal/day")
        
        # Test feature extraction
        features = bmr_predictor.extract_features(test_user, latest_entry, db)
        if features:
            print(f"🔍 Extracted {len(features)} features successfully")
            feature_names = bmr_predictor.feature_names
            print("   Key features:")
            for i, (name, value) in enumerate(zip(feature_names[:6], features[:6])):
                print(f"     {name}: {value:.2f}")
        else:
            print("❌ Failed to extract features")
            return False
        
        # Test training data preparation
        X, y = bmr_predictor.prepare_training_data(db)
        print(f"📈 Training data: {len(X)} samples")
        
        if len(X) < 10:
            print(f"❌ Insufficient training data: {len(X)} samples (need at least 10)")
            return False
        
        # Test model training
        print("🎯 Training BMR model...")
        training_result = bmr_predictor.train(db)
        
        if training_result["success"]:
            print("✅ Model training successful!")
            print(f"   Training samples: {training_result['training_samples']}")
            print(f"   Test samples: {training_result['test_samples']}")
            print(f"   MAE: {training_result['mae']:.2f}")
            print(f"   R² Score: {training_result['r2_score']:.3f}")
            
            # Show feature importance
            importance = training_result['feature_importance']
            sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)
            print("   Top 5 important features:")
            for feature, imp in sorted_features[:5]:
                print(f"     {feature}: {imp:.3f}")
        else:
            print(f"❌ Model training failed: {training_result['message']}")
            return False
        
        # Test prediction
        print("🔮 Testing BMR prediction...")
        predicted_bmr = bmr_predictor.predict_bmr(test_user, latest_entry, db)
        
        if predicted_bmr:
            print(f"✅ ML-predicted BMR: {predicted_bmr:.1f} cal/day")
            difference = predicted_bmr - standard_bmr
            print(f"   Difference from standard: {difference:+.1f} cal/day ({difference/standard_bmr*100:+.1f}%)")
        else:
            print("❌ BMR prediction failed")
            return False
        
        # Test insights
        print("💡 Testing BMR insights...")
        insights = bmr_predictor.get_bmr_insights(test_user, db)
        
        if "message" in insights:
            print(f"ℹ️ Insights: {insights['message']}")
        else:
            print("✅ BMR insights generated:")
            print(f"   Current BMR: {insights.get('current_bmr', 'N/A'):.1f} cal/day")
            print(f"   BMR trend: {insights.get('bmr_trend', 'N/A')}")
            print(f"   BMR change: {insights.get('bmr_change', 'N/A'):+.1f} cal")
            print(f"   Weight change: {insights.get('weight_change_lbs', 'N/A'):+.1f} lbs")
            print(f"   Analysis period: {insights.get('analysis_period_days', 'N/A')} days")
            
            if insights.get('recommendations'):
                print("   Recommendations:")
                for rec in insights['recommendations']:
                    print(f"     • {rec}")
        
        # Test updating existing entries with ML predictions
        print("🔄 Updating existing entries with ML predictions...")
        updated_count = 0
        
        for entry in user_entries:
            if not entry.bmr_predicted:  # Only update if not already predicted
                try:
                    predicted = bmr_predictor.predict_bmr(test_user, entry, db)
                    if predicted:
                        entry.bmr_predicted = predicted
                        updated_count += 1
                except Exception as e:
                    print(f"   ⚠️ Failed to predict for entry {entry.id}: {e}")
        
        if updated_count > 0:
            db.commit()
            print(f"✅ Updated {updated_count} entries with ML predictions")
        else:
            print("ℹ️ No entries needed ML prediction updates")
        
        print("\n🎉 BMR ML Implementation Test Completed Successfully!")
        print("\n📋 Summary:")
        print("   ✅ BMR prediction engine working")
        print("   ✅ Feature extraction functional")
        print("   ✅ Model training successful")
        print("   ✅ BMR predictions accurate")
        print("   ✅ Insights generation working")
        print("   ✅ Database integration complete")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("BMR ML Implementation Test with Existing Data")
    print("=" * 60)
    success = test_bmr_with_existing_data()
    sys.exit(0 if success else 1)
