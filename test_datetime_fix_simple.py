#!/usr/bin/env python3
"""
Simple test to verify the recorded_at fix works with existing database
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models import User, BiometricEntry
from app.schemas import BiometricEntryCreate

def test_datetime_fix():
    """Test that the recorded_at fix works with the existing database"""
    
    print("🧪 Testing Date/Time Fix with Existing Database...")
    
    # Connect to existing database
    engine = create_engine("sqlite:///./weightloss_tracker.db")
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Get an existing user
        user = db.query(User).first()
        if not user:
            print("❌ No users found in database")
            return False
        
        print(f"👤 Using user: {user.username}")
        
        # Test 1: Verify schema accepts recorded_at
        print("\n1️⃣ Testing BiometricEntryCreate schema...")
        
        custom_time = datetime.now() - timedelta(days=2, hours=5, minutes=30)
        
        try:
            entry_schema = BiometricEntryCreate(
                weight_lbs=180.5,
                body_fat_percentage=17.2,
                muscle_mass_lbs=78.5,
                recorded_at=custom_time
            )
            print(f"   ✅ Schema accepts recorded_at: {entry_schema.recorded_at}")
        except Exception as e:
            print(f"   ❌ Schema error: {e}")
            return False
        
        # Test 2: Create entry with custom timestamp
        print("\n2️⃣ Creating biometric entry with custom timestamp...")
        
        db_entry = BiometricEntry(
            user_id=user.id,
            recorded_at=custom_time,
            weight_lbs=180.5,
            body_fat_percentage=17.2,
            muscle_mass_lbs=78.5,
            water_percentage=62.0
        )
        
        db.add(db_entry)
        db.commit()
        db.refresh(db_entry)
        
        print(f"   ✅ Entry created with ID: {db_entry.id}")
        print(f"   📅 Custom timestamp: {custom_time}")
        print(f"   📅 Stored timestamp: {db_entry.recorded_at}")
        
        # Verify timestamp matches
        if db_entry.recorded_at == custom_time:
            print("   ✅ Custom timestamp correctly stored!")
        else:
            print("   ❌ Timestamp mismatch!")
            return False
        
        # Test 3: Create entry without custom timestamp (should use default)
        print("\n3️⃣ Creating entry without custom timestamp...")
        
        before_creation = datetime.now()
        
        db_entry_default = BiometricEntry(
            user_id=user.id,
            weight_lbs=179.8,
            body_fat_percentage=17.0
        )
        
        db.add(db_entry_default)
        db.commit()
        db.refresh(db_entry_default)
        
        after_creation = datetime.now()
        
        print(f"   📅 Entry timestamp: {db_entry_default.recorded_at}")
        
        # Check if timestamp is reasonable (between before and after creation)
        if before_creation <= db_entry_default.recorded_at <= after_creation:
            print("   ✅ Default timestamp behavior working!")
        else:
            print("   ⚠️ Default timestamp seems unusual")
        
        # Test 4: Check existing entries
        print("\n4️⃣ Checking existing biometric entries...")
        
        existing_entries = db.query(BiometricEntry).filter(
            BiometricEntry.user_id == user.id
        ).order_by(BiometricEntry.recorded_at.desc()).limit(5).all()
        
        print(f"   Found {len(existing_entries)} recent entries:")
        for i, entry in enumerate(existing_entries):
            print(f"     {i+1}. {entry.recorded_at} - Weight: {entry.weight_lbs} lbs")
        
        # Test 5: Update an existing entry's timestamp
        print("\n5️⃣ Testing timestamp update...")
        
        if existing_entries:
            entry_to_update = existing_entries[0]
            original_time = entry_to_update.recorded_at
            new_time = datetime.now() - timedelta(days=7, hours=2)
            
            entry_to_update.recorded_at = new_time
            db.commit()
            db.refresh(entry_to_update)
            
            print(f"   Original: {original_time}")
            print(f"   Updated:  {entry_to_update.recorded_at}")
            
            if entry_to_update.recorded_at == new_time:
                print("   ✅ Timestamp update successful!")
            else:
                print("   ❌ Timestamp update failed!")
                return False
        
        # Test 6: Verify the fix works for the frontend data format
        print("\n6️⃣ Testing frontend datetime format...")
        
        # Simulate what the frontend sends
        frontend_datetime_str = "2025-07-11T14:30:00"  # ISO format from frontend
        frontend_datetime = datetime.fromisoformat(frontend_datetime_str)
        
        frontend_entry = BiometricEntry(
            user_id=user.id,
            recorded_at=frontend_datetime,
            weight_lbs=181.2,
            body_fat_percentage=16.8
        )
        
        db.add(frontend_entry)
        db.commit()
        db.refresh(frontend_entry)
        
        print(f"   Frontend sent: {frontend_datetime_str}")
        print(f"   Stored as: {frontend_entry.recorded_at}")
        
        if frontend_entry.recorded_at == frontend_datetime:
            print("   ✅ Frontend datetime format handled correctly!")
        else:
            print("   ❌ Frontend datetime format issue!")
            return False
        
        print("\n🎉 Date/Time Fix Test Completed Successfully!")
        print("\n📋 Summary:")
        print("   ✅ BiometricEntryCreate schema accepts recorded_at field")
        print("   ✅ Custom timestamps are properly stored in database")
        print("   ✅ Default timestamp behavior works when no custom time provided")
        print("   ✅ Existing entries can be updated with new timestamps")
        print("   ✅ Frontend datetime format (ISO) is handled correctly")
        print("   ✅ Multiple entries with different timestamps work properly")
        
        print("\n🔧 The fix is working! Users can now:")
        print("   • Set custom date/time when adding biometric entries")
        print("   • Edit the date/time of existing entries")
        print("   • See the correct timestamps in their history")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("Date/Time Fix Test")
    print("=" * 40)
    success = test_datetime_fix()
    sys.exit(0 if success else 1)
