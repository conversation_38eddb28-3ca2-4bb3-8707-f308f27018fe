"""
Test the final optimizations:
1. Food entry with per-serving macros (not per 100g)
2. Biometric entries with custom date/time
"""
import requests
from datetime import datetime, date, time

API_BASE_URL = "http://localhost:8000/api"

def test_final_optimizations():
    """Test the final optimization features"""
    print("🧪 TESTING FINAL OPTIMIZATIONS\n")
    
    # Login first
    print("1️⃣ Logging in...")
    login_response = requests.post(
        f"{API_BASE_URL}/auth/login",
        data={"username": "demo", "password": "demo123"}
    )
    
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("   ✅ Login successful!")
    
    # Test 1: Add food with per-serving nutrition
    print("\n2️⃣ Testing food entry with per-serving nutrition...")
    
    # Example: Adding a banana with per-serving nutrition
    serving_size_g = 120  # 1 medium banana
    calories_per_serving = 105
    protein_per_serving = 1.3
    carbs_per_serving = 27.0
    fat_per_serving = 0.4
    
    # Convert to per-100g for API (this is what the UI now does automatically)
    calories_per_100g = (calories_per_serving * 100) / serving_size_g
    protein_per_100g = (protein_per_serving * 100) / serving_size_g
    carbs_per_100g = (carbs_per_serving * 100) / serving_size_g
    fat_per_100g = (fat_per_serving * 100) / serving_size_g
    
    food_data = {
        "name": "Test Banana",
        "brand": "Generic",
        "calories_per_100g": calories_per_100g,
        "protein_per_100g": protein_per_100g,
        "carbs_per_100g": carbs_per_100g,
        "fat_per_100g": fat_per_100g,
        "fiber_per_100g": 2.6 * 100 / serving_size_g,
        "sodium_per_100g": 1.0 * 100 / serving_size_g,
        "serving_size_g": serving_size_g,
        "serving_description": "1 medium banana"
    }
    
    food_response = requests.post(f"{API_BASE_URL}/nutrition/foods", headers=headers, json=food_data)
    if food_response.status_code == 200:
        food = food_response.json()
        print(f"   ✅ Food added: {food['name']}")
        print(f"   ✅ Serving: {food['serving_description']} ({food['serving_size_g']}g)")
        
        # Verify the per-serving calculation works
        actual_calories_per_serving = (food['calories_per_100g'] * food['serving_size_g']) / 100
        print(f"   ✅ Calories per serving: {actual_calories_per_serving:.0f} (expected: {calories_per_serving})")
        
        # Clean up
        food_id = food['id']
        delete_response = requests.delete(f"{API_BASE_URL}/nutrition/foods/{food_id}", headers=headers)
        if delete_response.status_code == 200:
            print(f"   ✅ Test food cleaned up")
    else:
        print(f"   ❌ Failed to add food: {food_response.text}")
        return False
    
    # Test 2: Add biometric entry with custom date/time
    print("\n3️⃣ Testing biometric entry with custom date/time...")
    
    # Create a historical entry (3 days ago)
    historical_date = date.today().replace(day=date.today().day-3)
    historical_time = time(8, 30)  # 8:30 AM
    historical_datetime = datetime.combine(historical_date, historical_time)
    
    biometric_data = {
        "weight_lbs": 172.5,
        "body_fat_percentage": 16.2,
        "muscle_mass_lbs": 79.8,
        "notes": "Historical entry test",
        "recorded_at": historical_datetime.isoformat()
    }
    
    bio_response = requests.post(f"{API_BASE_URL}/biometrics/", headers=headers, json=biometric_data)
    if bio_response.status_code == 200:
        entry = bio_response.json()
        print(f"   ✅ Biometric entry created with custom date/time")
        print(f"   ✅ Weight: {entry['weight_lbs']} lbs")
        print(f"   ✅ Recorded at: {entry.get('recorded_at', 'N/A')}")
        print(f"   ✅ BMI calculated: {entry.get('bmi', 'N/A'):.1f}")
        
        # Clean up
        entry_id = entry['id']
        delete_response = requests.delete(f"{API_BASE_URL}/biometrics/{entry_id}", headers=headers)
        if delete_response.status_code == 200:
            print(f"   ✅ Test entry cleaned up")
    else:
        print(f"   ❌ Failed to create biometric entry: {bio_response.text}")
        return False
    
    # Test 3: Verify nutrition entry still works with serving-based logging
    print("\n4️⃣ Testing nutrition logging with serving-based calculation...")
    
    # Get an existing food from the database
    foods_response = requests.get(f"{API_BASE_URL}/nutrition/foods", headers=headers)
    if foods_response.status_code == 200:
        foods = foods_response.json()
        if foods:
            food = foods[0]
            
            # Log 1.5 servings of this food
            nutrition_data = {
                "food_item_id": food['id'],
                "quantity": 1.5,
                "unit": "serving",
                "consumed_at": historical_datetime.isoformat(),
                "notes": "Test serving-based logging"
            }
            
            nutrition_response = requests.post(f"{API_BASE_URL}/nutrition/entries", headers=headers, json=nutrition_data)
            if nutrition_response.status_code == 200:
                entry = nutrition_response.json()
                print(f"   ✅ Nutrition entry created: 1.5 servings of {food['name']}")
                print(f"   ✅ Calories logged: {entry.get('calories', 'N/A'):.0f}")
                
                # Clean up
                entry_id = entry['id']
                delete_response = requests.delete(f"{API_BASE_URL}/nutrition/entries/{entry_id}", headers=headers)
                if delete_response.status_code == 200:
                    print(f"   ✅ Test entry cleaned up")
            else:
                print(f"   ❌ Failed to create nutrition entry: {nutrition_response.text}")
                return False
        else:
            print("   ⚠️ No foods in database to test with")
    else:
        print("   ❌ Failed to get foods")
        return False
    
    return True

def main():
    success = test_final_optimizations()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 ALL FINAL OPTIMIZATION TESTS PASSED! 🎉")
        print("\n✅ FINAL FEATURES WORKING:")
        print("   ✅ Food entry with per-serving nutrition input")
        print("   ✅ Automatic conversion to per-100g for storage")
        print("   ✅ Biometric entries with custom date/time")
        print("   ✅ Historical data entry capability")
        print("   ✅ Serving-based nutrition logging")
        print("\n🚀 ALL OPTIMIZATIONS COMPLETE!")
        print("\nThe app now provides:")
        print("   • Intuitive per-serving food entry")
        print("   • Historical biometric data entry")
        print("   • Complete date/time control for all logging")
        print("   • Smart meal composition with nutrition calculation")
        print("   • Professional-grade tracking capabilities")
    else:
        print("❌ SOME FINAL OPTIMIZATION TESTS FAILED")
        print("Check the error messages above for details.")

if __name__ == "__main__":
    main()
