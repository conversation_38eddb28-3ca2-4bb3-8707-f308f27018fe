#!/usr/bin/env python3
"""
Test the actual frontend to backend flow for recorded_at
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime, timedelta

# Test configuration
BASE_URL = "http://localhost:8000"

def test_frontend_backend_flow():
    """Test what actually happens when frontend sends data to backend"""
    
    print("🔍 Testing Frontend to Backend Flow...")
    
    # Step 1: Try to connect to the API
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"✅ API is accessible at {BASE_URL}")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Starting a simple test...")
        return test_without_api()
    
    # For now, let's test without the full API since it's not running
    return test_without_api()

def test_without_api():
    """Test the data flow without requiring the API to be running"""
    
    print("\n🧪 Testing Data Flow Logic...")
    
    # Step 1: Simulate what the frontend form would produce
    print("\n1️⃣ Simulating frontend form data...")
    
    # User selects a custom date and time
    user_selected_date = datetime.now().date() - timedelta(days=2)  # 2 days ago
    user_selected_time = datetime.strptime("08:30:00", "%H:%M:%S").time()  # 8:30 AM
    
    print(f"   User selected date: {user_selected_date}")
    print(f"   User selected time: {user_selected_time}")
    
    # Frontend combines date and time
    recorded_datetime = datetime.combine(user_selected_date, user_selected_time)
    print(f"   Combined datetime: {recorded_datetime}")
    
    # Frontend creates the request data
    entry_data = {
        "weight_lbs": 175.8,
        "body_fat_percentage": 15.5,
        "muscle_mass_lbs": 78.2,
        "water_percentage": 62.1,
        "recorded_at": recorded_datetime.isoformat()
    }
    
    print(f"   Frontend would send: {json.dumps(entry_data, indent=2)}")
    
    # Step 2: Test schema parsing (backend side)
    print("\n2️⃣ Testing backend schema parsing...")
    
    from app.schemas import BiometricEntryCreate
    
    try:
        # This is what happens when the backend receives the data
        entry_schema = BiometricEntryCreate(**entry_data)
        print(f"   ✅ Schema successfully parsed")
        print(f"   Schema recorded_at: {entry_schema.recorded_at}")
        print(f"   Type: {type(entry_schema.recorded_at)}")
        
        # Check if the datetime matches what the user selected
        if entry_schema.recorded_at == recorded_datetime:
            print("   ✅ Datetime preserved correctly through schema")
        else:
            print("   ❌ Datetime changed during schema parsing!")
            print(f"      Expected: {recorded_datetime}")
            print(f"      Got: {entry_schema.recorded_at}")
            return False
            
    except Exception as e:
        print(f"   ❌ Schema parsing failed: {e}")
        return False
    
    # Step 3: Test database storage simulation
    print("\n3️⃣ Testing database storage simulation...")
    
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    from app.models import User, BiometricEntry
    
    # Connect to existing database
    engine = create_engine("sqlite:///./weightloss_tracker.db")
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Get a user
        user = db.query(User).first()
        if not user:
            print("   ❌ No users found")
            return False
        
        print(f"   Using user: {user.username}")
        
        # Create the database entry exactly as the API would
        db_entry = BiometricEntry(
            user_id=user.id,
            recorded_at=entry_schema.recorded_at,  # Use the schema's datetime
            weight_lbs=entry_schema.weight_lbs,
            body_fat_percentage=entry_schema.body_fat_percentage,
            muscle_mass_lbs=entry_schema.muscle_mass_lbs,
            water_percentage=entry_schema.water_percentage
        )
        
        print(f"   DB entry recorded_at before save: {db_entry.recorded_at}")
        
        db.add(db_entry)
        db.commit()
        db.refresh(db_entry)
        
        print(f"   DB entry recorded_at after save: {db_entry.recorded_at}")
        print(f"   Entry ID: {db_entry.id}")
        
        # Check if it matches the user's selection
        if db_entry.recorded_at == recorded_datetime:
            print("   ✅ User's datetime correctly stored in database!")
        else:
            print("   ❌ User's datetime was changed during storage!")
            return False
        
    except Exception as e:
        print(f"   ❌ Database test failed: {e}")
        return False
    finally:
        db.close()
    
    # Step 4: Test what gets returned to frontend
    print("\n4️⃣ Testing API response simulation...")
    
    # Simulate what the API endpoint would return
    api_response = {
        "id": db_entry.id,
        "user_id": db_entry.user_id,
        "recorded_at": db_entry.recorded_at.isoformat() if db_entry.recorded_at else None,
        "weight_lbs": db_entry.weight_lbs,
        "body_fat_percentage": db_entry.body_fat_percentage,
        "muscle_mass_lbs": db_entry.muscle_mass_lbs,
        "water_percentage": db_entry.water_percentage,
        "bmr_calculated": db_entry.bmr_calculated,
        "bmr_predicted": db_entry.bmr_predicted
    }
    
    print(f"   API would return: {json.dumps(api_response, indent=2, default=str)}")
    
    # Step 5: Test frontend display parsing
    print("\n5️⃣ Testing frontend display parsing...")
    
    import pandas as pd
    
    # Simulate what the frontend does when displaying the data
    entries_data = [api_response]  # List of entries as returned by API
    df = pd.DataFrame(entries_data)
    
    print(f"   DataFrame before formatting:")
    print(f"   recorded_at: {df['recorded_at'].iloc[0]}")
    
    # Apply the same formatting as the frontend
    df['recorded_at'] = pd.to_datetime(df['recorded_at'], format='ISO8601').dt.strftime('%Y-%m-%d %H:%M')
    
    print(f"   DataFrame after formatting:")
    print(f"   recorded_at: {df['recorded_at'].iloc[0]}")
    
    # Check if the displayed time matches what the user selected
    expected_display = recorded_datetime.strftime('%Y-%m-%d %H:%M')
    actual_display = df['recorded_at'].iloc[0]
    
    if actual_display == expected_display:
        print(f"   ✅ Frontend display shows correct time: {actual_display}")
    else:
        print(f"   ❌ Frontend display shows wrong time!")
        print(f"      Expected: {expected_display}")
        print(f"      Actual: {actual_display}")
        return False
    
    print("\n🎉 Complete Flow Test Successful!")
    print("\n📋 Summary:")
    print(f"   User selected: {user_selected_date} at {user_selected_time}")
    print(f"   Frontend sent: {recorded_datetime.isoformat()}")
    print(f"   Backend stored: {db_entry.recorded_at}")
    print(f"   Frontend displays: {actual_display}")
    print("\n✅ The recorded_at flow is working correctly!")
    print("\n🤔 If you're still seeing server timestamps in the UI,")
    print("   it might be because:")
    print("   1. Users are not changing the default date/time in the form")
    print("   2. There are old entries with server timestamps")
    print("   3. The form is being submitted without the custom datetime")
    
    return True

if __name__ == "__main__":
    print("Frontend to Backend Flow Test")
    print("=" * 50)
    test_frontend_backend_flow()
