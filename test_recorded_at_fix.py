#!/usr/bin/env python3
"""
Test that recorded_at field is properly handled in biometric entries
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database import Base
from app.models import User, BiometricEntry
from app.schemas import BiometricEntryCreate

def test_recorded_at_field():
    """Test that custom recorded_at timestamps are properly stored"""
    
    print("🧪 Testing recorded_at Field Handling...")
    
    # Create in-memory SQLite database for testing
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(bind=engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Create a test user
        test_user = User(
            username="testuser_datetime",
            email="<EMAIL>",
            hashed_password="dummy",
            height_inches=69,
            date_of_birth=datetime.now().date() - timedelta(days=30*365),
            gender="male",
            activity_level="moderately_active"
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        # Test 1: Create entry with custom recorded_at
        print("\n1️⃣ Testing custom recorded_at timestamp...")
        custom_datetime = datetime.now() - timedelta(days=2, hours=3, minutes=30)
        
        # Test the schema first
        entry_data = BiometricEntryCreate(
            weight_lbs=175.5,
            body_fat_percentage=15.2,
            muscle_mass_lbs=76.0,
            recorded_at=custom_datetime
        )
        
        print(f"   Schema accepts recorded_at: {entry_data.recorded_at}")
        
        # Create database entry
        db_entry = BiometricEntry(
            user_id=test_user.id,
            recorded_at=entry_data.recorded_at,
            weight_lbs=entry_data.weight_lbs,
            body_fat_percentage=entry_data.body_fat_percentage,
            muscle_mass_lbs=entry_data.muscle_mass_lbs
        )
        
        db.add(db_entry)
        db.commit()
        db.refresh(db_entry)
        
        print(f"   ✅ Entry created with custom timestamp: {db_entry.recorded_at}")
        
        # Verify the timestamp matches
        if db_entry.recorded_at == custom_datetime:
            print("   ✅ Custom timestamp correctly stored!")
        else:
            print(f"   ❌ Timestamp mismatch! Expected: {custom_datetime}, Got: {db_entry.recorded_at}")
            return False
        
        # Test 2: Create entry without recorded_at (should use default)
        print("\n2️⃣ Testing default recorded_at behavior...")
        before_creation = datetime.now()
        
        entry_data_no_time = BiometricEntryCreate(
            weight_lbs=174.0,
            body_fat_percentage=15.0
        )
        
        db_entry_default = BiometricEntry(
            user_id=test_user.id,
            recorded_at=entry_data_no_time.recorded_at,  # This should be None
            weight_lbs=entry_data_no_time.weight_lbs,
            body_fat_percentage=entry_data_no_time.body_fat_percentage
        )
        
        db.add(db_entry_default)
        db.commit()
        db.refresh(db_entry_default)
        
        after_creation = datetime.now()
        
        print(f"   Entry created with recorded_at: {db_entry_default.recorded_at}")
        
        # Check if it used server default (should be close to current time)
        if db_entry_default.recorded_at:
            time_diff = abs((db_entry_default.recorded_at - datetime.now()).total_seconds())
            if time_diff < 60:  # Within 1 minute
                print("   ✅ Default timestamp behavior working!")
            else:
                print(f"   ⚠️ Default timestamp seems off by {time_diff} seconds")
        else:
            print("   ❌ No timestamp was set!")
            return False
        
        # Test 3: Verify entries are stored with different timestamps
        print("\n3️⃣ Testing multiple entries with different timestamps...")
        
        all_entries = db.query(BiometricEntry).filter(
            BiometricEntry.user_id == test_user.id
        ).order_by(BiometricEntry.recorded_at).all()
        
        print(f"   Found {len(all_entries)} entries:")
        for i, entry in enumerate(all_entries):
            print(f"     Entry {i+1}: {entry.recorded_at} - Weight: {entry.weight_lbs} lbs")
        
        # Verify they have different timestamps
        if len(set(entry.recorded_at for entry in all_entries)) == len(all_entries):
            print("   ✅ All entries have unique timestamps!")
        else:
            print("   ❌ Some entries have duplicate timestamps!")
            return False
        
        # Test 4: Test updating recorded_at
        print("\n4️⃣ Testing recorded_at updates...")
        
        first_entry = all_entries[0]
        original_time = first_entry.recorded_at
        new_time = datetime.now() - timedelta(days=5)
        
        # Update the recorded_at field
        first_entry.recorded_at = new_time
        db.commit()
        db.refresh(first_entry)
        
        if first_entry.recorded_at == new_time:
            print(f"   ✅ Successfully updated recorded_at from {original_time} to {new_time}")
        else:
            print(f"   ❌ Failed to update recorded_at!")
            return False
        
        print("\n🎉 All recorded_at tests passed!")
        print("\n📋 Summary:")
        print("   ✅ Custom recorded_at timestamps are properly stored")
        print("   ✅ Default behavior works when no timestamp provided")
        print("   ✅ Multiple entries can have different timestamps")
        print("   ✅ recorded_at field can be updated")
        print("   ✅ BiometricEntryCreate schema accepts recorded_at field")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("Testing recorded_at Field Handling")
    print("=" * 50)
    success = test_recorded_at_field()
    sys.exit(0 if success else 1)
